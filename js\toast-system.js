/**
 * 全局Toast通知系统
 * 统一的消息提示组件，支持多种类型和自定义样式
 */

class ToastSystem {
    constructor() {
        this.container = null;
        this.toasts = new Map();
        this.init();
    }

    /**
     * 初始化Toast容器
     */
    init() {
        // 确保DOM已加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
            return;
        }

        // 尝试使用现有的toast容器
        this.container = document.getElementById('toastContainer') || document.getElementById('globalToastContainer');

        if (!this.container) {
            // 创建新容器
            this.container = document.createElement('div');
            this.container.className = 'toast-container';
            this.container.id = 'globalToastContainer';

            // 确保body存在
            if (document.body) {
                document.body.appendChild(this.container);
            } else {
                console.error('Toast系统初始化失败：document.body不存在');
                return;
            }
        }
    }

    /**
     * 显示Toast消息
     * @param {string} type - 消息类型: success, error, warning, info
     * @param {string} title - 标题
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)，0表示不自动关闭
     * @param {Object} options - 额外选项
     */
    show(type = 'info', title = '', message = '', duration = 5000, options = {}) {
        const toastId = this.generateId();
        const toast = this.createToast(toastId, type, title, message, options);
        
        this.container.appendChild(toast);
        this.toasts.set(toastId, toast);

        // 触发显示动画
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.hide(toastId);
            }, duration);
        }

        return toastId;
    }

    /**
     * 创建Toast元素
     */
    createToast(id, type, title, message, options) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.dataset.toastId = id;

        const iconMap = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const colorMap = {
            success: '#10b981',
            error: '#ef4444', 
            warning: '#f59e0b',
            info: '#3b82f6'
        };

        toast.innerHTML = `
            <button class="toast-close" onclick="window.toastSystem.hide('${id}')">×</button>
            <div class="toast-header">
                <div class="toast-icon" style="color: ${colorMap[type]}">
                    ${iconMap[type]}
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    ${message ? `<div class="toast-message">${message}</div>` : ''}
                </div>
            </div>
            <div class="toast-progress" style="background-color: ${colorMap[type]}"></div>
        `;

        return toast;
    }

    /**
     * 隐藏Toast
     */
    hide(toastId) {
        const toast = this.toasts.get(toastId);
        if (!toast) return;

        toast.classList.remove('show');
        toast.classList.add('hide');

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            this.toasts.delete(toastId);
        }, 300);
    }

    /**
     * 清除所有Toast
     */
    clear() {
        this.toasts.forEach((toast, id) => {
            this.hide(id);
        });
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return 'toast_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 快捷方法
     */
    success(title, message, duration = 5000) {
        return this.show('success', title, message, duration);
    }

    error(title, message, duration = 8000) {
        return this.show('error', title, message, duration);
    }

    warning(title, message, duration = 6000) {
        return this.show('warning', title, message, duration);
    }

    info(title, message, duration = 5000) {
        return this.show('info', title, message, duration);
    }
}

// 全局实例
window.toastSystem = new ToastSystem();

// 兼容性方法 - 与现有代码兼容
window.showToast = function(type, title, message, duration) {
    return window.toastSystem.show(type, title, message, duration);
};

// 简化的全局方法
window.showMessage = function(message, type = 'info', duration = 5000) {
    return window.toastSystem.show(type, '', message, duration);
};

window.showSuccess = function(message, duration = 5000) {
    return window.toastSystem.success('成功', message, duration);
};

window.showError = function(message, duration = 8000) {
    return window.toastSystem.error('错误', message, duration);
};

window.showWarning = function(message, duration = 6000) {
    return window.toastSystem.warning('警告', message, duration);
};

window.showInfo = function(message, duration = 5000) {
    return window.toastSystem.info('提示', message, duration);
};

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ToastSystem;
}
