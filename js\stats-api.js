/**
 * 统计功能相关API接口
 */

/**
 * 统计API服务类
 */
class StatsAPI {
    constructor(httpClient) {
        this.client = httpClient;
        this.statsEnabled = localStorage.getItem('statsDisabled') !== 'true'; // 检查本地存储
    }

    /**
     * 检查统计功能是否启用
     * @returns {boolean} 是否启用
     */
    isStatsEnabled() {
        return this.statsEnabled;
    }

    /**
     * 禁用统计功能
     */
    disableStats() {
        this.statsEnabled = false;
        localStorage.setItem('statsDisabled', 'true');
    }

    /**
     * 启用统计功能
     */
    enableStats() {
        this.statsEnabled = true;
        localStorage.removeItem('statsDisabled');
    }

    /**
     * 获取系统统计信息
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 系统统计结果
     */
    async getSystemStats(params = {}) {
        try {
            const queryParams = {
                startDate: params.startDate,
                endDate: params.endDate,
                granularity: params.granularity || 'day', // hour, day, week, month
                metrics: params.metrics || ['users', 'sessions', 'requests', 'errors']
            };

            const response = await this.client.get('/stats/system', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        overview: response.data.overview || {},
                        metrics: response.data.metrics || {},
                        trends: response.data.trends || [],
                        performance: response.data.performance || {},
                        errors: response.data.errors || {},
                        usage: response.data.usage || {}
                    }
                };
            }

            throw new APIError('获取系统统计失败', 400);
        } catch (error) {
            console.error('获取系统统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取系统统计失败', 0);
        }
    }

    /**
     * 获取用户活动统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 用户活动统计结果
     */
    async getUserActivityStats(params = {}) {
        try {
            const queryParams = {
                userId: params.userId,
                startDate: params.startDate,
                endDate: params.endDate,
                groupBy: params.groupBy || 'day', // hour, day, week, month
                includeDetails: params.includeDetails || false
            };

            const response = await this.client.get('/stats/user-activity', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        summary: response.data.summary || {},
                        activities: response.data.activities || [],
                        sessions: response.data.sessions || [],
                        features: response.data.features || {},
                        timeline: response.data.timeline || []
                    }
                };
            }

            throw new APIError('获取用户活动统计失败', 400);
        } catch (error) {
            console.error('获取用户活动统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取用户活动统计失败', 0);
        }
    }

    /**
     * 获取功能使用统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 功能使用统计结果
     */
    async getFeatureUsageStats(params = {}) {
        try {
            const queryParams = {
                feature: params.feature,
                startDate: params.startDate,
                endDate: params.endDate,
                groupBy: params.groupBy || 'day',
                includeUserBreakdown: params.includeUserBreakdown || false
            };

            const response = await this.client.get('/stats/feature-usage', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        overview: response.data.overview || {},
                        features: response.data.features || [],
                        trends: response.data.trends || [],
                        topFeatures: response.data.topFeatures || [],
                        userBreakdown: response.data.userBreakdown || []
                    }
                };
            }

            throw new APIError('获取功能使用统计失败', 400);
        } catch (error) {
            console.error('获取功能使用统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取功能使用统计失败', 0);
        }
    }

    /**
     * 获取数据处理统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 数据处理统计结果
     */
    async getDataProcessingStats(params = {}) {
        try {
            const queryParams = {
                startDate: params.startDate,
                endDate: params.endDate,
                processType: params.processType, // upload, analysis, export
                status: params.status, // success, failed, pending
                groupBy: params.groupBy || 'day'
            };

            const response = await this.client.get('/stats/data-processing', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        summary: response.data.summary || {},
                        processing: response.data.processing || [],
                        performance: response.data.performance || {},
                        errors: response.data.errors || [],
                        fileTypes: response.data.fileTypes || {}
                    }
                };
            }

            throw new APIError('获取数据处理统计失败', 400);
        } catch (error) {
            console.error('获取数据处理统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取数据处理统计失败', 0);
        }
    }

    /**
     * 获取性能统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 性能统计结果
     */
    async getPerformanceStats(params = {}) {
        try {
            const queryParams = {
                startDate: params.startDate,
                endDate: params.endDate,
                metric: params.metric, // response_time, throughput, error_rate
                endpoint: params.endpoint,
                groupBy: params.groupBy || 'hour'
            };

            const response = await this.client.get('/stats/performance', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        overview: response.data.overview || {},
                        metrics: response.data.metrics || [],
                        endpoints: response.data.endpoints || [],
                        alerts: response.data.alerts || [],
                        trends: response.data.trends || []
                    }
                };
            }

            throw new APIError('获取性能统计失败', 400);
        } catch (error) {
            console.error('获取性能统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取性能统计失败', 0);
        }
    }

    /**
     * 获取错误统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 错误统计结果
     */
    async getErrorStats(params = {}) {
        try {
            const queryParams = {
                startDate: params.startDate,
                endDate: params.endDate,
                errorType: params.errorType,
                severity: params.severity, // low, medium, high, critical
                groupBy: params.groupBy || 'hour'
            };

            const response = await this.client.get('/stats/errors', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        summary: response.data.summary || {},
                        errors: response.data.errors || [],
                        trends: response.data.trends || [],
                        topErrors: response.data.topErrors || [],
                        resolution: response.data.resolution || {}
                    }
                };
            }

            throw new APIError('获取错误统计失败', 400);
        } catch (error) {
            console.error('获取错误统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取错误统计失败', 0);
        }
    }

    /**
     * 获取实时统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 实时统计结果
     */
    async getRealTimeStats(params = {}) {
        try {
            const queryParams = {
                metrics: params.metrics || ['active_users', 'requests_per_minute', 'error_rate'],
                interval: params.interval || 60 // seconds
            };

            const response = await this.client.get('/stats/realtime', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        timestamp: response.data.timestamp,
                        activeUsers: response.data.activeUsers || 0,
                        requestsPerMinute: response.data.requestsPerMinute || 0,
                        errorRate: response.data.errorRate || 0,
                        systemLoad: response.data.systemLoad || {},
                        alerts: response.data.alerts || []
                    }
                };
            }

            throw new APIError('获取实时统计失败', 400);
        } catch (error) {
            console.error('获取实时统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取实时统计失败', 0);
        }
    }

    /**
     * 记录用户行为
     * @param {Object} action - 行为数据
     * @returns {Promise<Object>} 记录结果
     */
    async trackUserAction(action) {
        try {
            // 检查是否启用统计功能
            if (!this.isStatsEnabled()) {
                return { success: false, message: '统计功能未启用' };
            }

            const requestData = {
                action: action.action,
                feature: action.feature,
                page: action.page || window.location.pathname,
                timestamp: action.timestamp || new Date().toISOString(),
                metadata: {
                    userAgent: navigator.userAgent,
                    screenResolution: `${screen.width}x${screen.height}`,
                    ...action.metadata
                }
            };

            const response = await this.client.post('/stats/track', requestData);

            if (response.success) {
                return {
                    success: true,
                    message: '行为记录成功'
                };
            }

            throw new APIError('记录用户行为失败', 400);
        } catch (error) {
            console.warn('记录用户行为失败:', error);

            // 如果是404错误，说明统计接口不存在，禁用统计功能
            if (error.status === 404) {
                this.disableStats();
                console.warn('统计接口不存在，已禁用统计功能');
            }

            // 统计功能失败不应该影响主要功能，只记录警告
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 生成统计报告
     * @param {Object} params - 报告参数
     * @returns {Promise<Object>} 报告生成结果
     */
    async generateReport(params) {
        try {
            const requestData = {
                reportType: params.reportType, // daily, weekly, monthly, custom
                startDate: params.startDate,
                endDate: params.endDate,
                metrics: params.metrics || [],
                format: params.format || 'pdf', // pdf, excel, json
                includeCharts: params.includeCharts !== false,
                recipients: params.recipients || []
            };

            const response = await this.client.post('/stats/reports', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        reportId: response.data.reportId,
                        status: response.data.status,
                        downloadUrl: response.data.downloadUrl,
                        estimatedTime: response.data.estimatedTime
                    }
                };
            }

            throw new APIError('生成统计报告失败', 400);
        } catch (error) {
            console.error('生成统计报告失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('生成统计报告失败', 0);
        }
    }

    /**
     * 格式化统计数据
     * @param {Object} data - 原始数据
     * @param {string} type - 数据类型
     * @returns {Object} 格式化后的数据
     */
    formatStatsData(data, type) {
        switch (type) {
            case 'percentage':
                return {
                    value: parseFloat(data.value || 0).toFixed(2) + '%',
                    raw: parseFloat(data.value || 0)
                };
            case 'number':
                return {
                    value: this.formatNumber(data.value || 0),
                    raw: parseInt(data.value || 0)
                };
            case 'duration':
                return {
                    value: this.formatDuration(data.value || 0),
                    raw: parseFloat(data.value || 0)
                };
            case 'bytes':
                return {
                    value: this.formatBytes(data.value || 0),
                    raw: parseInt(data.value || 0)
                };
            default:
                return data;
        }
    }

    /**
     * 格式化数字
     * @param {number} num - 数字
     * @returns {string} 格式化后的数字
     */
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * 格式化持续时间
     * @param {number} ms - 毫秒
     * @returns {string} 格式化后的持续时间
     */
    formatDuration(ms) {
        if (ms < 1000) {
            return ms + 'ms';
        } else if (ms < 60000) {
            return (ms / 1000).toFixed(1) + 's';
        } else if (ms < 3600000) {
            return (ms / 60000).toFixed(1) + 'm';
        } else {
            return (ms / 3600000).toFixed(1) + 'h';
        }
    }

    /**
     * 格式化字节大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 创建统计API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.StatsAPI = new StatsAPI(window.APIService.client);
}
