/**
 * WebSocket实时通信客户端
 */

/**
 * WebSocket客户端类
 */
class WebSocketClient {
    constructor(baseUrl = 'wss://cugzcfwwhuiq.sealoshzh.site') {
        this.baseUrl = baseUrl;
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.heartbeatInterval = null;
        this.heartbeatTimeout = 30000; // 30秒
        this.messageHandlers = new Map();
        this.connectionPromise = null;
        
        // 绑定方法
        this.onOpen = this.onOpen.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }

    /**
     * 连接WebSocket
     * @param {string} token - 认证令牌
     * @returns {Promise<boolean>} 连接结果
     */
    async connect(token) {
        if (this.isConnected) {
            return true;
        }

        if (this.connectionPromise) {
            return this.connectionPromise;
        }

        this.connectionPromise = new Promise((resolve, reject) => {
            try {
                const wsUrl = `${this.baseUrl}/ws?token=${encodeURIComponent(token)}`;
                this.socket = new WebSocket(wsUrl);
                
                this.socket.onopen = (event) => {
                    this.onOpen(event);
                    resolve(true);
                };
                
                this.socket.onmessage = this.onMessage;
                this.socket.onclose = this.onClose;
                this.socket.onerror = (event) => {
                    this.onError(event);
                    reject(new Error('WebSocket连接失败'));
                };

                // 连接超时处理
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.socket.close();
                        reject(new Error('WebSocket连接超时'));
                    }
                }, 10000);

            } catch (error) {
                console.error('WebSocket连接错误:', error);
                reject(error);
            }
        });

        return this.connectionPromise;
    }

    /**
     * 断开连接
     */
    disconnect() {
        this.isConnected = false;
        this.connectionPromise = null;
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }

    /**
     * 发送消息
     * @param {Object} message - 消息对象
     * @returns {boolean} 发送结果
     */
    send(message) {
        if (!this.isConnected || !this.socket) {
            console.warn('WebSocket未连接，无法发送消息');
            return false;
        }

        try {
            const messageStr = JSON.stringify({
                ...message,
                timestamp: Date.now()
            });
            this.socket.send(messageStr);
            return true;
        } catch (error) {
            console.error('发送WebSocket消息失败:', error);
            return false;
        }
    }

    /**
     * 订阅消息类型
     * @param {string} type - 消息类型
     * @param {Function} handler - 处理函数
     */
    subscribe(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }

    /**
     * 取消订阅
     * @param {string} type - 消息类型
     * @param {Function} handler - 处理函数
     */
    unsubscribe(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * 连接打开事件
     */
    onOpen(event) {
        console.log('WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.connectionPromise = null;
        
        // 启动心跳
        this.startHeartbeat();
        
        // 触发连接事件
        this.triggerHandlers('connection', { type: 'connected' });
    }

    /**
     * 消息接收事件
     */
    onMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('收到WebSocket消息:', message);
            
            // 处理心跳响应
            if (message.type === 'pong') {
                return;
            }
            
            // 触发对应的消息处理器
            this.triggerHandlers(message.type, message);
            
        } catch (error) {
            console.error('解析WebSocket消息失败:', error);
        }
    }

    /**
     * 连接关闭事件
     */
    onClose(event) {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        this.isConnected = false;
        this.connectionPromise = null;
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        // 触发断开连接事件
        this.triggerHandlers('connection', { type: 'disconnected', code: event.code, reason: event.reason });
        
        // 自动重连
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        }
    }

    /**
     * 连接错误事件
     */
    onError(event) {
        console.error('WebSocket连接错误:', event);
        this.triggerHandlers('connection', { type: 'error', error: event });
    }

    /**
     * 尝试重连
     */
    async attemptReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${delay}ms后重试`);
        
        setTimeout(async () => {
            try {
                const token = localStorage.getItem('authToken');
                if (token) {
                    await this.connect(token);
                }
            } catch (error) {
                console.error('重连失败:', error);
            }
        }, delay);
    }

    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'ping' });
            }
        }, this.heartbeatTimeout);
    }

    /**
     * 触发消息处理器
     */
    triggerHandlers(type, message) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            handlers.forEach(handler => {
                try {
                    handler(message);
                } catch (error) {
                    console.error(`消息处理器错误 (${type}):`, error);
                }
            });
        }
    }

    /**
     * 订阅串口数据
     * @param {string} portId - 串口ID
     * @param {Function} callback - 数据回调函数
     */
    subscribeSerialData(portId, callback) {
        this.subscribe('serial_data', (message) => {
            if (message.portId === portId) {
                callback(message.data);
            }
        });
        
        // 发送订阅请求
        this.send({
            type: 'subscribe',
            channel: 'serial_data',
            portId: portId
        });
    }

    /**
     * 取消订阅串口数据
     * @param {string} portId - 串口ID
     */
    unsubscribeSerialData(portId) {
        this.send({
            type: 'unsubscribe',
            channel: 'serial_data',
            portId: portId
        });
    }

    /**
     * 订阅分析进度
     * @param {string} taskId - 任务ID
     * @param {Function} callback - 进度回调函数
     */
    subscribeAnalysisProgress(taskId, callback) {
        this.subscribe('analysis_progress', (message) => {
            if (message.taskId === taskId) {
                callback(message.progress);
            }
        });
        
        // 发送订阅请求
        this.send({
            type: 'subscribe',
            channel: 'analysis_progress',
            taskId: taskId
        });
    }

    /**
     * 订阅系统通知
     * @param {Function} callback - 通知回调函数
     */
    subscribeSystemNotifications(callback) {
        this.subscribe('system_notification', callback);
        
        // 发送订阅请求
        this.send({
            type: 'subscribe',
            channel: 'system_notifications'
        });
    }

    /**
     * 订阅实时统计
     * @param {Function} callback - 统计回调函数
     */
    subscribeRealTimeStats(callback) {
        this.subscribe('realtime_stats', callback);
        
        // 发送订阅请求
        this.send({
            type: 'subscribe',
            channel: 'realtime_stats'
        });
    }

    /**
     * 发送串口数据
     * @param {string} portId - 串口ID
     * @param {string} data - 数据
     */
    sendSerialData(portId, data) {
        this.send({
            type: 'serial_send',
            portId: portId,
            data: data
        });
    }

    /**
     * 获取连接状态
     * @returns {boolean} 连接状态
     */
    getConnectionStatus() {
        return this.isConnected;
    }
}

// 创建全局WebSocket客户端实例
if (typeof window !== 'undefined') {
    window.WebSocketClient = WebSocketClient;
    
    // 创建默认实例
    window.wsClient = new WebSocketClient();
    
    // 自动连接（如果有token）
    document.addEventListener('DOMContentLoaded', async () => {
        const token = localStorage.getItem('authToken');
        if (token) {
            try {
                await window.wsClient.connect(token);
                console.log('WebSocket自动连接成功');
            } catch (error) {
                console.warn('WebSocket自动连接失败:', error);
            }
        }
    });
}
