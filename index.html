<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - 登录</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://at.alicdn.com/t/c/font_3966894_i1j7nk7f2vo.css">
    <!-- 备用图标库，确保图标能够显示 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- SVG图标备用方案 -->
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/toast-system.css">
    <link rel="stylesheet" href="css/toast-system.css">
    <!-- 添加动画库，但减少使用 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <!-- 添加Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <!-- 页面加载动画 -->
    <div class="page-loader">
        <div class="loader-content">
            <div class="spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-dot"></div>
            </div>
            <p>正在加载...</p>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <div class="particles-container" id="particles-js"></div>
    
    <!-- 添加动态背景元素，减少动画强度 -->
    <div class="animated-bg">
        <div class="animated-shape shape1"></div>
        <div class="animated-shape shape2"></div>
        <div class="animated-shape shape3"></div>
        <div class="animated-shape shape4"></div>
    </div>

    <!-- 数学图形装饰元素 -->
    <div class="decoration-elements">
        <div class="floating-circle"></div>
        <div class="floating-square"></div>
        <div class="floating-triangle"></div>
        <div class="floating-dots"></div>
        <!-- 简洁的几何背景装饰 -->
        <div class="geometric-bg">
            <div class="floating-circle circle1"></div>
            <div class="floating-circle circle2"></div>
            <div class="floating-circle circle3"></div>
            <div class="floating-triangle triangle1"></div>
            <div class="floating-triangle triangle2"></div>
            <div class="gradient-orb orb1"></div>
            <div class="gradient-orb orb2"></div>
        </div>

        <!-- 粒子特效容器 -->
        <div class="particles-container">
            <div class="particle particle1"></div>
            <div class="particle particle2"></div>
            <div class="particle particle3"></div>
            <div class="particle particle4"></div>
            <div class="particle particle5"></div>
            <div class="particle particle6"></div>
            <div class="particle particle7"></div>
            <div class="particle particle8"></div>
            <div class="particle particle9"></div>
            <div class="particle particle10"></div>
            <div class="particle particle11"></div>
            <div class="particle particle12"></div>
            <div class="particle particle13"></div>
            <div class="particle particle14"></div>
            <div class="particle particle15"></div>
        </div>

        <!-- 星星点缀效果 -->
        <div class="stars-container">
            <div class="star star1">✦</div>
            <div class="star star2">✧</div>
            <div class="star star3">✦</div>
            <div class="star star4">✧</div>
            <div class="star star5">✦</div>
            <div class="star star6">✧</div>
            <div class="star star7">✦</div>
            <div class="star star8">✧</div>
            <div class="star star9">✦</div>
            <div class="star star10">✧</div>
            <div class="star star11">✦</div>
            <div class="star star12">✧</div>
        </div>

        <!-- 随机几何图形装饰 -->
        <div class="geometric-decorations">
            <div class="geo-shape circle-deco c1"></div>
            <div class="geo-shape circle-deco c2"></div>
            <div class="geo-shape circle-deco c3"></div>
            <div class="geo-shape circle-deco c4"></div>
            <div class="geo-shape square-deco s1"></div>
            <div class="geo-shape square-deco s2"></div>
            <div class="geo-shape square-deco s3"></div>
            <div class="geo-shape triangle-deco t1"></div>
            <div class="geo-shape triangle-deco t2"></div>
            <div class="geo-shape triangle-deco t3"></div>
            <div class="geo-shape hexagon-deco h1"></div>
            <div class="geo-shape hexagon-deco h2"></div>
        </div>
    </div>
    
    <div class="login-container">
        <div class="login-content">
            <div class="login-header">
                <div class="logo-wrapper">
                    <div class="logo data-analytics-logo">
                        <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                            <!-- 外圆环 -->
                            <circle cx="60" cy="60" r="55" fill="none" stroke="url(#gradient1)" stroke-width="3" opacity="0.8"/>

                            <!-- 数据图表 -->
                            <g transform="translate(20, 30)">
                                <!-- 柱状图 -->
                                <rect x="10" y="40" width="8" height="20" fill="url(#gradient2)" rx="2"/>
                                <rect x="22" y="30" width="8" height="30" fill="url(#gradient2)" rx="2"/>
                                <rect x="34" y="35" width="8" height="25" fill="url(#gradient2)" rx="2"/>
                                <rect x="46" y="25" width="8" height="35" fill="url(#gradient2)" rx="2"/>
                                <rect x="58" y="20" width="8" height="40" fill="url(#gradient2)" rx="2"/>
                            </g>

                            <!-- 数据波形 -->
                            <path d="M 20 80 Q 30 70, 40 75 T 60 70 T 80 75 T 100 70"
                                  stroke="url(#gradient3)" stroke-width="3" fill="none" opacity="0.9"/>

                            <!-- 数据点 -->
                            <circle cx="30" cy="75" r="3" fill="#2c7be5"/>
                            <circle cx="50" cy="70" r="3" fill="#00d9ff"/>
                            <circle cx="70" cy="75" r="3" fill="#ff7b00"/>
                            <circle cx="90" cy="70" r="3" fill="#ff0090"/>

                            <!-- 中心图标 -->
                            <g transform="translate(45, 45)">
                                <rect x="0" y="0" width="30" height="20" rx="3" fill="none" stroke="url(#gradient4)" stroke-width="2"/>
                                <line x1="5" y1="25" x2="25" y2="25" stroke="url(#gradient4)" stroke-width="2"/>
                                <circle cx="8" cy="28" r="2" fill="#2c7be5"/>
                                <circle cx="15" cy="28" r="2" fill="#00d9ff"/>
                                <circle cx="22" cy="28" r="2" fill="#ff7b00"/>
                            </g>

                            <!-- 渐变定义 -->
                            <defs>
                                <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#2c7be5;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#00d9ff;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="gradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#2c7be5;stop-opacity:0.9" />
                                    <stop offset="100%" style="stop-color:#1e6bb8;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#2c7be5;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#00d9ff;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#ff7b00;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#2c7be5;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#00d9ff;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                </div>
                <h1>欢迎使用</h1>
                <p class="login-subtitle">
                    专业的信号处理与数据分析平台
                </p>
                <div class="header-divider"></div>
            </div>
            <div class="login-form">
                <form id="loginForm">
                    <div class="input-group">
                        <span class="input-icon user-icon">📧</span>
                        <input type="email" id="email" placeholder="邮箱地址" required>
                        <span class="input-focus-border"></span>
                        <span class="validation-message">请输入邮箱地址</span>
                        <span class="validation-icon success">
                            <span class="icon icon-check"></span>
                        </span>
                        <span class="validation-icon error">
                            <span class="icon icon-close"></span>
                        </span>
                    </div>
                    <div class="input-group">
                        <span class="input-icon lock-icon">🔒</span>
                        <input type="password" id="password" placeholder="密码" required>
                        <span class="input-focus-border"></span>

                        <span class="validation-message">请输入密码</span>

                        <div class="password-strength">
                            <div class="password-strength-bar"></div>
                            <span class="password-strength-text">密码强度：弱</span>
                        </div>
                    </div>
                    <div class="form-options">
                        <div class="remember-me">
                            <label class="custom-checkbox">
                                <input type="checkbox" id="remember">
                                记住我
                            </label>
                        </div>
                        <a href="forgot-password.html" class="forgot-link">忘记密码?</a>
                    </div>
                    <button type="submit" class="btn login-btn">
                        <span class="btn-text">登录</span>
                        <span class="btn-loader">
                            <span class="loader-dot"></span>
                            <span class="loader-dot"></span>
                            <span class="loader-dot"></span>
                        </span>
                    </button>
                </form>
                <div class="register-link">
                    没有账号? <a href="register.html">立即注册</a>
                </div>
                
                <div class="social-login">
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="微信登录">
                            <span class="iconfont icon-wechat">
                                <span class="icon icon-wechat"></span>
                            </span>
                            <div class="hover-glow"></div>
                        </a>
                        <a href="#" class="social-icon" title="QQ登录">
                            <span class="iconfont icon-qq">
                                <span class="icon icon-qq"></span>
                            </span>
                            <div class="hover-glow"></div>
                        </a>
                        <a href="#" class="social-icon" title="邮箱登录">
                            <span class="iconfont icon-email">
                                <span class="icon icon-email"></span>
                            </span>
                            <div class="hover-glow"></div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        

    </div>
    
    <div class="copyright">
        © 2025 数据分析平台 | 专业的信号处理与数据分析工具
    </div>

    <!-- 添加消息提示框 -->
    <div class="message-box"></div>

    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/auth-api.js"></script>
    <script src="js/global-error-handler.js"></script>
    <script src="js/loading-manager.js"></script>
    <script src="js/toast-system.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
