/**
 * 认证相关API接口
 */

/**
 * 认证API服务类
 */
class AuthAPI {
    constructor(httpClient) {
        this.client = httpClient;
    }

    /**
     * 用户注册
     * @param {Object} userData - 用户注册数据
     * @param {string} userData.username - 用户名，3-50字符
     * @param {string} userData.email - 邮箱地址
     * @param {string} userData.password - 密码，至少6字符
     * @param {string} userData.confirmPassword - 确认密码
     * @returns {Promise<Object>} 注册结果
     */
    async register(userData) {
        try {
            // 验证输入数据
            this.validateUserData(userData);

            const response = await this.client.post('/auth/register', userData, {
                auth: false // 注册不需要认证
            });

            if (response.success && response.data) {
                // 注册成功后自动保存Token
                if (response.data.token) {
                    this.client.tokenManager.setToken(response.data.token);
                    
                    // 保存用户信息
                    localStorage.setItem('currentUser', response.data.username);
                    localStorage.setItem('userEmail', response.data.email);
                    localStorage.setItem('userId', response.data.userId);
                    localStorage.setItem('loginTime', new Date().toISOString());
                }
                
                return {
                    success: true,
                    message: response.message || '注册成功',
                    user: {
                        id: response.data.userId,
                        username: response.data.username,
                        email: response.data.email
                    },
                    token: response.data.token
                };
            }

            throw new APIError('注册失败', 400);
        } catch (error) {
            console.error('注册失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            // 根据HTTP状态码返回具体错误
            if (error.status) {
                switch (error.status) {
                    case 409:
                        throw new APIError('该邮箱已被注册，请使用其他邮箱', 409, 'EMAIL_EXISTS');
                    case 422:
                        throw new APIError('输入信息格式不正确，请检查后重试', 422, 'VALIDATION_ERROR');
                    case 429:
                        throw new APIError('注册请求过于频繁，请稍后再试', 429, 'RATE_LIMIT');
                    case 500:
                        throw new APIError('服务器暂时不可用，请稍后重试', 500, 'SERVER_ERROR');
                    default:
                        throw new APIError('注册失败，请重试', error.status);
                }
            }

            // 处理网络错误
            throw new APIError('网络连接异常，请检查网络后重试', 0, 'NETWORK_ERROR');
        }
    }

    /**
     * 用户登录
     * @param {Object} credentials - 登录凭据
     * @param {string} credentials.email - 邮箱地址
     * @param {string} credentials.password - 密码
     * @returns {Promise<Object>} 登录结果
     */
    async login(credentials) {
        try {
            // 验证输入数据
            this.validateCredentials(credentials);

            const response = await this.client.post('/auth/login', credentials, {
                auth: false // 登录不需要认证
            });

            if (response.success && response.data) {
                // 登录成功后保存Token和用户信息
                if (response.data.token) {
                    this.client.tokenManager.setToken(response.data.token);
                }
                
                // 保存用户信息
                if (response.data.user) {
                    localStorage.setItem('currentUser', response.data.user.username);
                    localStorage.setItem('userEmail', response.data.user.email);
                    localStorage.setItem('userId', response.data.user.id);
                }
                localStorage.setItem('loginTime', new Date().toISOString());
                
                return {
                    success: true,
                    message: '登录成功',
                    user: response.data.user,
                    token: response.data.token
                };
            }

            throw new APIError('登录失败', 401);
        } catch (error) {
            console.error('登录失败:', error);

            if (error instanceof APIError) {
                throw error;
            }

            // 根据HTTP状态码返回具体错误
            if (error.status) {
                switch (error.status) {
                    case 401:
                        throw new APIError('邮箱或密码错误，请检查后重试', 401, 'INVALID_CREDENTIALS');
                    case 404:
                        throw new APIError('该邮箱尚未注册，请先注册账号', 404, 'USER_NOT_FOUND');
                    case 423:
                        throw new APIError('账号已被锁定，请联系管理员', 423, 'ACCOUNT_LOCKED');
                    case 403:
                        throw new APIError('账号已被禁用，请联系管理员', 403, 'ACCOUNT_DISABLED');
                    case 429:
                        throw new APIError('登录尝试次数过多，请稍后再试', 429, 'TOO_MANY_ATTEMPTS');
                    case 500:
                        throw new APIError('服务器暂时不可用，请稍后重试', 500, 'SERVER_ERROR');
                    default:
                        throw new APIError('登录失败，请重试', error.status);
                }
            }

            // 处理网络错误
            throw new APIError('网络连接异常，请检查网络后重试', 0, 'NETWORK_ERROR');
        }
    }

    /**
     * 用户登出
     * @returns {Promise<Object>} 登出结果
     */
    async logout() {
        try {
            // 调用后端登出接口（如果有的话）
            try {
                await this.client.post('/auth/logout', {});
            } catch (error) {
                // 即使后端登出失败，也要清除本地数据
                console.warn('后端登出失败:', error);
            }
            
            // 清除本地存储的认证信息
            this.client.tokenManager.clearToken();
            
            return {
                success: true,
                message: '登出成功'
            };
        } catch (error) {
            console.error('登出失败:', error);
            
            // 即使出错也要清除本地数据
            this.client.tokenManager.clearToken();
            
            return {
                success: true,
                message: '登出成功'
            };
        }
    }

    /**
     * 刷新Token
     * @returns {Promise<Object>} 刷新结果
     */
    async refreshToken() {
        try {
            const refreshToken = this.client.tokenManager.refreshToken;
            if (!refreshToken) {
                throw new APIError('没有刷新令牌', 401);
            }

            const response = await this.client.post('/auth/refresh', {}, {
                headers: {
                    'Authorization': `Bearer ${refreshToken}`
                },
                auth: false
            });

            if (response.success && response.data.token) {
                this.client.tokenManager.setToken(
                    response.data.token,
                    response.data.refreshToken
                );
                
                return {
                    success: true,
                    token: response.data.token
                };
            }

            throw new APIError('Token刷新失败', 401);
        } catch (error) {
            console.error('Token刷新失败:', error);
            
            // 刷新失败，清除所有认证信息
            this.client.tokenManager.clearToken();
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('Token刷新失败', 401);
        }
    }

    /**
     * 获取当前用户信息
     * @returns {Promise<Object>} 用户信息
     */
    async getCurrentUser() {
        try {
            const response = await this.client.get('/auth/me');
            
            if (response.success && response.data) {
                // 更新本地存储的用户信息
                localStorage.setItem('currentUser', response.data.username);
                localStorage.setItem('userEmail', response.data.email);
                localStorage.setItem('userId', response.data.id);
                
                return {
                    success: true,
                    user: response.data
                };
            }

            throw new APIError('获取用户信息失败', 400);
        } catch (error) {
            console.error('获取用户信息失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取用户信息失败', 0);
        }
    }

    /**
     * 检查用户是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        return this.client.tokenManager.isTokenValid();
    }

    /**
     * 获取本地存储的用户信息
     * @returns {Object|null} 用户信息
     */
    getLocalUserInfo() {
        const username = localStorage.getItem('currentUser');
        const email = localStorage.getItem('userEmail');
        const userId = localStorage.getItem('userId');
        const loginTime = localStorage.getItem('loginTime');

        if (username && email && userId) {
            return {
                id: userId,
                username: username,
                email: email,
                loginTime: loginTime
            };
        }

        return null;
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 验证密码强度
     * @param {string} password - 密码
     * @returns {Object} 验证结果
     */
    validatePassword(password) {
        const result = {
            isValid: false,
            strength: 'weak',
            score: 0,
            requirements: {
                length: password.length >= 6,
                lowercase: /[a-z]/.test(password),
                uppercase: /[A-Z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[^A-Za-z0-9]/.test(password)
            }
        };

        // 计算密码强度分数
        Object.values(result.requirements).forEach(met => {
            if (met) result.score += 1;
        });

        // 确定密码强度等级
        if (result.score >= 4) {
            result.strength = 'strong';
            result.isValid = true;
        } else if (result.score >= 3) {
            result.strength = 'good';
            result.isValid = true;
        } else if (result.score >= 2) {
            result.strength = 'medium';
            result.isValid = result.requirements.length;
        } else {
            result.strength = 'weak';
            result.isValid = result.requirements.length;
        }

        return result;
    }

    /**
     * 验证用户注册数据
     * @param {Object} userData - 用户数据
     */
    validateUserData(userData) {
        if (!userData.email || !userData.password || !userData.username) {
            throw new APIError('请填写完整的注册信息', 400, 'VALIDATION_ERROR');
        }

        if (userData.password.length < 6) {
            throw new APIError('密码长度至少6位', 400, 'WEAK_PASSWORD');
        }

        // 检查邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userData.email)) {
            throw new APIError('邮箱格式不正确', 400, 'INVALID_EMAIL');
        }
    }

    /**
     * 验证登录凭据
     * @param {Object} credentials - 登录凭据
     */
    validateCredentials(credentials) {
        if (!credentials.email || !credentials.password) {
            throw new APIError('请输入邮箱和密码', 400, 'VALIDATION_ERROR');
        }

        // 检查邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(credentials.email)) {
            throw new APIError('邮箱格式不正确', 400, 'INVALID_EMAIL');
        }
    }
}

// 创建认证API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.AuthAPI = new AuthAPI(window.APIService.client);
}
