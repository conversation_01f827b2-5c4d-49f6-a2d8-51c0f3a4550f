/**
 * 信号处理相关API接口
 */

/**
 * 信号处理API服务类
 */
class SignalAPI {
    constructor(httpClient) {
        this.client = httpClient;
    }

    /**
     * FFT分析
     * @param {Object} params - FFT分析参数
     * @returns {Promise<Object>} FFT分析结果
     */
    async performFFT(params) {
        try {
            const requestData = {
                data: params.data,
                sampleRate: params.sampleRate || 1000,
                windowType: params.windowType || 'hanning',
                nfft: params.nfft || 1024,
                overlap: params.overlap || 0.5
            };

            const response = await this.client.post('/signal/fft', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: 'FFT分析完成',
                    data: {
                        frequencies: response.data.frequencies,
                        magnitudes: response.data.magnitudes,
                        phases: response.data.phases,
                        powerSpectrum: response.data.powerSpectrum,
                        dominantFrequency: response.data.dominantFrequency,
                        analysisParams: response.data.analysisParams
                    }
                };
            }

            throw new APIError('FFT分析失败', 400);
        } catch (error) {
            console.error('FFT分析失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('FFT分析失败', 0);
        }
    }

    /**
     * 信号降噪
     * @param {Object} params - 降噪参数
     * @returns {Promise<Object>} 降噪结果
     */
    async denoiseSignal(params) {
        try {
            const requestData = {
                data: params.data,
                method: params.method || 'wavelet',
                threshold: params.threshold || 0.1,
                waveletType: params.waveletType || 'db4',
                levels: params.levels || 4,
                filterType: params.filterType || 'soft'
            };

            const response = await this.client.post('/signal/denoise', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '信号降噪完成',
                    data: {
                        denoisedData: response.data.denoisedData,
                        noiseData: response.data.noiseData,
                        snrImprovement: response.data.snrImprovement,
                        denoiseParams: response.data.denoiseParams
                    }
                };
            }

            throw new APIError('信号降噪失败', 400);
        } catch (error) {
            console.error('信号降噪失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('信号降噪失败', 0);
        }
    }

    /**
     * 包络分析
     * @param {Object} params - 包络分析参数
     * @returns {Promise<Object>} 包络分析结果
     */
    async envelopeAnalysis(params) {
        try {
            const requestData = {
                data: params.data,
                method: params.method || 'hilbert',
                filterLowFreq: params.filterLowFreq || 1,
                filterHighFreq: params.filterHighFreq || 100,
                smoothingWindow: params.smoothingWindow || 10
            };

            const response = await this.client.post('/signal/envelope', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '包络分析完成',
                    data: {
                        envelope: response.data.envelope,
                        instantaneousFreq: response.data.instantaneousFreq,
                        instantaneousPhase: response.data.instantaneousPhase,
                        analysisParams: response.data.analysisParams
                    }
                };
            }

            throw new APIError('包络分析失败', 400);
        } catch (error) {
            console.error('包络分析失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('包络分析失败', 0);
        }
    }

    /**
     * 滤波处理
     * @param {Object} params - 滤波参数
     * @returns {Promise<Object>} 滤波结果
     */
    async filterSignal(params) {
        try {
            const requestData = {
                data: params.data,
                filterType: params.filterType || 'lowpass',
                cutoffFreq: params.cutoffFreq || 50,
                order: params.order || 4,
                sampleRate: params.sampleRate || 1000,
                ripple: params.ripple || 0.1
            };

            const response = await this.client.post('/signal/filter', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '滤波处理完成',
                    data: {
                        filteredData: response.data.filteredData,
                        filterResponse: response.data.filterResponse,
                        filterParams: response.data.filterParams
                    }
                };
            }

            throw new APIError('滤波处理失败', 400);
        } catch (error) {
            console.error('滤波处理失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('滤波处理失败', 0);
        }
    }

    /**
     * 时频分析
     * @param {Object} params - 时频分析参数
     * @returns {Promise<Object>} 时频分析结果
     */
    async timeFrequencyAnalysis(params) {
        try {
            const requestData = {
                data: params.data,
                method: params.method || 'stft',
                windowSize: params.windowSize || 256,
                overlap: params.overlap || 0.75,
                sampleRate: params.sampleRate || 1000
            };

            const response = await this.client.post('/signal/timefreq', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '时频分析完成',
                    data: {
                        timeFreqMatrix: response.data.timeFreqMatrix,
                        timeAxis: response.data.timeAxis,
                        freqAxis: response.data.freqAxis,
                        analysisParams: response.data.analysisParams
                    }
                };
            }

            throw new APIError('时频分析失败', 400);
        } catch (error) {
            console.error('时频分析失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('时频分析失败', 0);
        }
    }

    /**
     * 信号特征提取
     * @param {Object} params - 特征提取参数
     * @returns {Promise<Object>} 特征提取结果
     */
    async extractFeatures(params) {
        try {
            const requestData = {
                data: params.data,
                features: params.features || ['rms', 'peak', 'crest_factor', 'kurtosis', 'skewness'],
                sampleRate: params.sampleRate || 1000
            };

            const response = await this.client.post('/signal/features', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '特征提取完成',
                    data: response.data
                };
            }

            throw new APIError('特征提取失败', 400);
        } catch (error) {
            console.error('特征提取失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('特征提取失败', 0);
        }
    }

    /**
     * 批量信号处理
     * @param {Object} params - 批量处理参数
     * @returns {Promise<Object>} 批量处理结果
     */
    async batchProcess(params) {
        try {
            const requestData = {
                dataList: params.dataList,
                operations: params.operations,
                commonParams: params.commonParams || {}
            };

            const response = await this.client.post('/signal/batch', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '批量处理完成',
                    data: response.data
                };
            }

            throw new APIError('批量处理失败', 400);
        } catch (error) {
            console.error('批量处理失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('批量处理失败', 0);
        }
    }

    /**
     * 获取处理历史
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 处理历史
     */
    async getProcessHistory(params = {}) {
        try {
            const queryParams = {
                page: params.page || 1,
                limit: params.limit || 20,
                operation: params.operation,
                startDate: params.startDate,
                endDate: params.endDate
            };

            const response = await this.client.get('/signal/history', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取处理历史失败', 400);
        } catch (error) {
            console.error('获取处理历史失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取处理历史失败', 0);
        }
    }

    /**
     * 验证信号数据格式
     * @param {Array} data - 信号数据
     * @returns {Object} 验证结果
     */
    validateSignalData(data) {
        if (!Array.isArray(data)) {
            return {
                isValid: false,
                error: '数据必须是数组格式'
            };
        }

        if (data.length === 0) {
            return {
                isValid: false,
                error: '数据不能为空'
            };
        }

        // 检查数据点格式
        const firstPoint = data[0];
        const hasXY = typeof firstPoint === 'object' && 'x' in firstPoint && 'y' in firstPoint;
        const isNumeric = typeof firstPoint === 'number';

        if (!hasXY && !isNumeric) {
            return {
                isValid: false,
                error: '数据格式不正确，应为数值数组或包含x,y属性的对象数组'
            };
        }

        return {
            isValid: true,
            format: hasXY ? 'xy' : 'numeric',
            length: data.length
        };
    }

    /**
     * 转换数据格式
     * @param {Array} data - 原始数据
     * @param {string} targetFormat - 目标格式 ('numeric' | 'xy')
     * @returns {Array} 转换后的数据
     */
    convertDataFormat(data, targetFormat = 'numeric') {
        if (!Array.isArray(data) || data.length === 0) {
            return [];
        }

        const firstPoint = data[0];
        const isXY = typeof firstPoint === 'object' && 'x' in firstPoint && 'y' in firstPoint;

        if (targetFormat === 'numeric') {
            if (isXY) {
                return data.map(point => point.y);
            } else {
                return data.filter(val => typeof val === 'number');
            }
        } else if (targetFormat === 'xy') {
            if (!isXY) {
                return data.map((val, index) => ({
                    x: index,
                    y: typeof val === 'number' ? val : 0
                }));
            } else {
                return data;
            }
        }

        return data;
    }
}

// 创建信号处理API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.SignalAPI = new SignalAPI(window.APIService.client);
}
