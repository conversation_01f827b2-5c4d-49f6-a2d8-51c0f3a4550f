<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - 找回密码</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://at.alicdn.com/t/c/font_3966894_i1j7nk7f2vo.css">
    <!-- 备用图标库，确保图标能够显示 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- SVG图标备用方案 -->
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <!-- 添加动画库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
</head>
<body class="login-page">
    <!-- 页面加载动画 -->
    <div class="page-loader">
        <div class="loader-content">
            <div class="spinner"></div>
            <p>正在加载...</p>
        </div>
    </div>

    <div class="particles-container" id="particles-js"></div>
    
    <!-- 添加动态背景元素 -->
    <div class="animated-bg">
        <div class="animated-shape shape1"></div>
        <div class="animated-shape shape2"></div>
        <div class="animated-shape shape3"></div>
        <div class="animated-shape shape4"></div>
        <div class="animated-shape shape5"></div>
        <div class="animated-shape shape6"></div>
    </div>
    
    <div class="login-container animate__animated animate__fadeIn">
        <div class="login-content">
            <div class="login-header">
                <img src="img/logo.svg" alt="数据分析平台" class="logo animate__animated animate__pulse animate__infinite animate__slower">
                <h1 class="animate__animated animate__fadeInUp">找回密码</h1>
                <p class="login-subtitle animate__animated animate__fadeIn animate__delay-1s">
                    我们将向您发送重置链接
                </p>
            </div>
            <div class="login-form animate__animated animate__fadeIn animate__delay-1s">
                <div class="reset-steps">
                    <div class="reset-step active" id="step-email">
                        <p class="step-desc">请输入您的电子邮箱，我们将向您发送一个密码重置链接。</p>
                        <form id="resetForm">
                            <div class="input-group">
                                <span class="iconfont icon-mail"></span>
                                <span class="icon icon-mail"></span>
                                <input type="email" id="reset_email" placeholder="电子邮箱" required>
                                <span class="input-focus-border"></span>
                            </div>
                            <button type="submit" class="btn login-btn">
                                <span class="btn-text">发送重置链接</span>
                                <span class="btn-loader">
                                    <span class="loader-dot"></span>
                                    <span class="loader-dot"></span>
                                    <span class="loader-dot"></span>
                                </span>
                            </button>
                        </form>
                    </div>
                    
                    <div class="reset-step" id="step-confirm" style="display: none;">
                        <div class="success-icon animate__animated">
                            <span class="iconfont icon-check"></span>
                            <span class="icon icon-check"></span>
                        </div>
                        <h2 class="animate__animated animate__fadeIn">邮件已发送</h2>
                        <p class="step-desc animate__animated animate__fadeIn animate__delay-1s">
                            我们已向您的邮箱发送了一封包含密码重置链接的电子邮件。
                            请检查您的收件箱，并点击邮件中的链接重置您的密码。
                        </p>
                        <p class="step-desc animate__animated animate__fadeIn animate__delay-1s">
                            没有收到邮件？请检查您的垃圾邮件文件夹，或
                            <a href="#" id="resendLink">点击此处</a> 重新发送。
                        </p>
                        <button class="btn btn-outline animate__animated animate__fadeIn animate__delay-2s" id="backToLogin">
                            <span class="iconfont icon-arrow-left"></span>
                            <span class="icon icon-arrow-left"></span>
                            返回登录
                        </button>
                    </div>
                </div>
                
                <div class="register-link">
                    <a href="index.html">返回登录</a> 或 <a href="register.html">注册新账号</a>
                </div>
            </div>
        </div>
    </div>

    <div class="copyright">
        © 2023 数据分析平台 | 专业的信号处理与数据分析工具
    </div>

    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/forgot-password.js"></script>
    <script src="js/navigation.js"></script>
</body>
</html>
