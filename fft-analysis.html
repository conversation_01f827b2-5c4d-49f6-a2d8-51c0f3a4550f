<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析平台 - FFT频谱分析</title>
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Toast通知系统 -->
    <link rel="stylesheet" href="css/toast-system.css">

    <!-- Chart.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom@2.0.1/dist/chartjs-plugin-zoom.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.2.1/dist/chartjs-plugin-annotation.min.js"></script>

    <!-- SheetJS库用于处理Excel文件 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <!-- API服务模块 -->
    <script src="js/api-service.js"></script>
    <script src="js/auth-api.js"></script>
    <script src="js/auth-guard.js"></script>
    <script src="js/signal-api.js"></script>
    <script src="js/export-api.js"></script>
    <script src="js/stats-api.js"></script>
    <script src="js/websocket-client.js"></script>
    <script src="js/global-error-handler.js"></script>
    <script src="js/loading-manager.js"></script>
    <script src="js/toast-system.js"></script>

    <!-- 使用自实现的FFT算法，无需外部DSP库 -->

    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            overflow-x: auto;
            overflow-y: auto;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(147, 197, 253, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(196, 181, 253, 0.25) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(167, 243, 208, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 60% 80%, rgba(254, 202, 202, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 90% 60%, rgba(253, 230, 138, 0.15) 0%, transparent 60%),
                linear-gradient(135deg, #f8fafc 0%, #f1f5f9 20%, #e2e8f0 40%, #f0f9ff 60%, #fef3c7 80%, #fce7f3 100%);
            background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
            color: #1e293b;
            overflow-x: auto;
            overflow-y: auto;
            position: relative;
            animation: backgroundShift 30s ease-in-out infinite;
            scroll-behavior: smooth;
            touch-action: pan-x pan-y;
        }

        @keyframes backgroundShift {
            0%, 100% {
                background-position: 0% 0%, 100% 100%, 50% 50%, 25% 75%, 90% 60%, 0% 0%;
            }
            16% {
                background-position: 10% 20%, 90% 80%, 30% 70%, 45% 55%, 80% 40%, 16% 16%;
            }
            33% {
                background-position: 30% 40%, 70% 60%, 60% 30%, 65% 35%, 70% 80%, 33% 33%;
            }
            50% {
                background-position: 60% 80%, 40% 20%, 80% 60%, 25% 75%, 60% 20%, 50% 50%;
            }
            66% {
                background-position: 80% 60%, 20% 40%, 40% 80%, 75% 25%, 50% 60%, 66% 66%;
            }
            83% {
                background-position: 40% 20%, 60% 80%, 20% 40%, 55% 65%, 40% 80%, 83% 83%;
            }
        }

        /* 增强动态背景元素 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .animated-shape {
            position: absolute;
            border-radius: 50%;
            animation: float 25s ease-in-out infinite;
        }

        .shape1 {
            width: 400px;
            height: 400px;
            top: 5%;
            left: 5%;
            background: radial-gradient(circle, rgba(147, 197, 253, 0.4) 0%, rgba(191, 219, 254, 0.15) 100%);
            animation-delay: 0s;
        }

        .shape2 {
            width: 300px;
            height: 300px;
            top: 50%;
            right: 5%;
            background: radial-gradient(circle, rgba(196, 181, 253, 0.35) 0%, rgba(221, 214, 254, 0.15) 100%);
            animation-delay: 8s;
        }

        .shape3 {
            width: 250px;
            height: 250px;
            bottom: 10%;
            left: 15%;
            background: radial-gradient(circle, rgba(167, 243, 208, 0.4) 0%, rgba(209, 250, 229, 0.15) 100%);
            animation-delay: 16s;
        }

        .shape4 {
            width: 350px;
            height: 350px;
            top: 25%;
            right: 25%;
            background: radial-gradient(circle, rgba(254, 202, 202, 0.35) 0%, rgba(254, 226, 226, 0.15) 100%);
            animation-delay: 12s;
        }

        .shape5 {
            width: 200px;
            height: 200px;
            top: 70%;
            left: 60%;
            background: radial-gradient(circle, rgba(253, 230, 138, 0.4) 0%, rgba(254, 240, 138, 0.15) 100%);
            animation-delay: 4s;
        }

        .shape6 {
            width: 180px;
            height: 180px;
            top: 15%;
            left: 70%;
            background: radial-gradient(circle, rgba(252, 231, 243, 0.35) 0%, rgba(253, 242, 248, 0.15) 100%);
            animation-delay: 20s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.4;
            }
            25% {
                transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.1);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-60px) translateX(-10px) rotate(180deg) scale(0.9);
                opacity: 0.6;
            }
            75% {
                transform: translateY(-20px) translateX(-30px) rotate(270deg) scale(1.05);
                opacity: 0.3;
            }
        }

        /* 粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: particleFloat 15s linear infinite;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* 主容器布局 */
        .main-container {
            display: grid;
            grid-template-columns: 260px 1fr;
            min-height: 100vh;
            height: auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            min-width: 1750px;
        }

        /* 固定水平滚动条 */
        .horizontal-scrollbar {
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 25px !important;
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-top: 2px solid rgba(59, 130, 246, 0.3) !important;
            z-index: 9999 !important;
            overflow-x: auto !important;
            overflow-y: hidden !important;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
            display: block !important;
            visibility: visible !important;
            width: 100% !important;
        }

        .horizontal-scrollbar-content {
            height: 20px;
            width: 3000px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
            min-width: 3000px;
        }

        /* 确保滚动条可见 */
        .horizontal-scrollbar::-webkit-scrollbar {
            height: 12px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6);
            border-radius: 6px;
        }

        .horizontal-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8);
        }

        /* 页面加载动画 */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 25%, #bae6fd 50%, #7dd3fc 75%, #38bdf8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.6s ease, visibility 0.6s ease;
        }

        .page-loader.loaded {
            opacity: 0;
            visibility: hidden;
        }

        .loader-content {
            text-align: center;
            color: #0369a1;
        }

        .spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .spinner-ring {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #0369a1;
            animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
        }

        .spinner-ring:nth-child(1) {
            animation-delay: 0s;
        }

        .spinner-ring:nth-child(2) {
            width: 80%;
            height: 80%;
            border-top-color: #0284c7;
            animation-delay: 0.15s;
            animation-direction: reverse;
        }

        .spinner-ring:nth-child(3) {
            width: 60%;
            height: 60%;
            border-top-color: #0ea5e9;
            animation-delay: 0.3s;
        }

        .spinner-dot {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #0369a1;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(3, 105, 161, 0.5);
            animation: spinnerDot 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes spinnerDot {
            0%, 100% {
                transform: scale(0.8);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .loading-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            letter-spacing: 1px;
        }

        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(3, 105, 161, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #0369a1, #0ea5e9);
            border-radius: 2px;
            animation: loadingProgress 2s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
                transform: translateX(-100%);
            }
            50% {
                width: 100%;
                transform: translateX(0%);
            }
            100% {
                width: 100%;
                transform: translateX(100%);
            }
        }

        /* 滑动导航控件 */
        .slide-navigation {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 9998;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .slide-nav-btn {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #3b82f6;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .slide-nav-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
        }

        .slide-nav-btn:active {
            transform: scale(0.95);
        }

        /* 页面指示器 */
        .page-indicator {
            position: fixed;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9998;
            display: flex;
            gap: 8px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .page-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(59, 130, 246, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-dot.active {
            background: #3b82f6;
            transform: scale(1.2);
        }

        .page-dot:hover {
            background: rgba(59, 130, 246, 0.7);
            transform: scale(1.1);
        }

        /* 左侧导航栏 */
        .sidebar {
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(25px);
            border-right: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                4px 0 25px rgba(0, 0, 0, 0.06),
                2px 0 15px rgba(59, 130, 246, 0.08);
            overflow-y: auto;
            position: relative;
            padding: 15px 12px;
        }

        .sidebar-header {
            padding: 20px 15px;
            text-align: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            border-radius: 16px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 导航菜单 */
        .nav-section {
            padding: 25px 20px;
        }

        .nav-category {
            margin-bottom: 25px;
        }

        .category-header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .category-header:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .category-icon {
            font-size: 18px;
            color: #3b82f6;
            margin-right: 12px;
            width: 20px;
        }

        .category-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }

        .category-arrow {
            font-size: 12px;
            color: #3b82f6;
            transition: transform 0.3s ease;
        }

        .category-header.expanded .category-arrow {
            transform: rotate(180deg);
        }

        .nav-submenu {
            list-style: none;
            margin-top: 10px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .nav-category.expanded .nav-submenu {
            max-height: 300px;
        }

        .nav-item {
            margin: 5px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 45px;
            color: #64748b;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .nav-link:hover {
            color: #3b82f6;
            transform: translateX(8px);
        }

        .nav-icon {
            font-size: 16px;
            margin-right: 10px;
            width: 18px;
            color: #94a3b8;
            transition: color 0.3s ease;
        }

        .nav-link:hover .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.15) 100%);
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .nav-link.active .nav-icon {
            color: #3b82f6;
        }

        .nav-link.active::before {
            width: 100%;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* 意见反馈样式 */
        .feedback-section {
            position: absolute;
            bottom: 20px;
            left: 12px;
            right: 12px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            color: #3b82f6;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .feedback-link:hover {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .feedback-icon {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .feedback-link:hover .feedback-icon {
            transform: scale(1.1);
        }

        .feedback-text {
            font-size: 14px;
            font-weight: 600;
        }

        /* 主内容区域样式 */
        .main-content {
            padding: 15px 20px 30px 20px;
            background: rgba(255, 255, 255, 0.02);
            min-height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            overflow-x: auto;
            overflow-y: auto;
        }

        /* 页面标题 */
        .page-header {
            margin-bottom: 30px;
            padding: 25px 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            box-shadow:
                0 10px 40px rgba(0, 0, 0, 0.06),
                0 4px 20px rgba(59, 130, 246, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-description {
            font-size: 16px;
            color: #64748b;
            line-height: 1.6;
        }

        /* 内容布局 */
        .content-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 25px;
            min-width: 1750px;
            width: max-content;
            align-items: stretch;
            padding: 20px;
            padding-bottom: 40px; /* 为底部滚动条留出空间 */
            flex: 1;
            min-height: 0;
            height: calc(100vh - 40px); /* 只减去底部滚动条的高度 */
        }

        /* 左侧控制面板 */
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow-y: auto;
            padding-bottom: 10px;
            padding-right: 5px;
            flex-shrink: 0;
            min-height: 0;
            height: 100%; /* 使用全部可用高度 */
            max-height: calc(100vh - 40px); /* 限制最大高度 */
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏Webkit浏览器的滚动条 */
        .control-panel::-webkit-scrollbar {
            display: none;
        }

        .control-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 20px;
            box-shadow:
                0 10px 40px rgba(0, 0, 0, 0.06),
                0 4px 20px rgba(59, 130, 246, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: visible;
            flex-shrink: 0;
            width: 100%;
            box-sizing: border-box;
        }

        .control-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
        }

        .control-card:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow:
                0 25px 70px rgba(0, 0, 0, 0.12),
                0 8px 30px rgba(59, 130, 246, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .control-card h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-card h3 i {
            color: #3b82f6;
            font-size: 20px;
        }

        /* 右侧图表区域 */
        .charts-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-height: 0;
            overflow-y: auto;
            height: 100%; /* 使用全部可用高度 */
            max-height: calc(100vh - 40px); /* 限制最大高度 */
            padding-bottom: 10px;
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        /* 隐藏Webkit浏览器的滚动条 */
        .charts-area::-webkit-scrollbar {
            display: none;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            padding: 25px;
            box-shadow:
                0 10px 40px rgba(0, 0, 0, 0.06),
                0 4px 20px rgba(59, 130, 246, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
        }

        .chart-container:hover {
            transform: translateY(-3px);
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 6px 25px rgba(59, 130, 246, 0.12),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-title i {
            color: #3b82f6;
            font-size: 20px;
        }

        .chart-canvas {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .chart-canvas.large {
            height: 400px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #0ea5e9);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            background: linear-gradient(135deg, #2563eb, #0284c7);
        }

        .btn-outline {
            background: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }

        .btn-outline:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        /* 表单控件样式 */
        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 30px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.05) 100%);
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .upload-area.dragover {
            border-color: #0ea5e9;
            background: linear-gradient(135deg, rgba(14, 165, 233, 0.15) 0%, rgba(59, 130, 246, 0.15) 100%);
        }

        .upload-icon {
            font-size: 48px;
            color: #3b82f6;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 14px;
            color: #64748b;
        }

        /* 数据预览表格 */
        .data-preview {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            margin-top: 15px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
        }

        .data-table th {
            background: rgba(59, 130, 246, 0.1);
            font-weight: 600;
            color: #1e293b;
            position: sticky;
            top: 0;
        }

        .data-table td {
            color: #374151;
        }

        .data-table tr:hover {
            background: rgba(59, 130, 246, 0.05);
        }
    </style>
</head>
<body>
    <!-- 页面加载动画 -->
    <div class="page-loader" id="pageLoader">
        <div class="loader-content">
            <div class="spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-dot"></div>
            </div>
            <div class="loading-text">正在加载FFT频谱分析工具...</div>
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>
        </div>
    </div>

    <!-- 增强动态背景元素 -->
    <div class="animated-bg">
        <div class="animated-shape shape1"></div>
        <div class="animated-shape shape2"></div>
        <div class="animated-shape shape3"></div>
        <div class="animated-shape shape4"></div>
        <div class="animated-shape shape5"></div>
        <div class="animated-shape shape6"></div>
    </div>

    <!-- 粒子效果 -->
    <div class="particles" id="particles"></div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="sidebar-title">数据分析平台</h1>
            </div>

            <nav class="nav-section">
                <!-- 信号可视化分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-visualization">
                        <i class="fas fa-chart-line category-icon"></i>
                        <span class="category-title">信号可视化</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="data.html" class="nav-link" data-page="data">
                                <i class="fas fa-upload nav-icon"></i>
                                <span class="nav-text">数据上传</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serial-debug.html" class="nav-link" data-page="serial-debug">
                                <i class="fas fa-terminal nav-icon"></i>
                                <span class="nav-text">串口调试</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号处理分类 -->
                <div class="nav-category">
                    <div class="category-header" data-category="signal-processing">
                        <i class="fas fa-wave-square category-icon"></i>
                        <span class="category-title">信号处理</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="signal-denoise.html" class="nav-link" data-page="signal-denoise">
                                <i class="fas fa-magic nav-icon"></i>
                                <span class="nav-text">信号去噪</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 信号分析分类 -->
                <div class="nav-category expanded">
                    <div class="category-header expanded" data-category="signal-analysis">
                        <i class="fas fa-chart-area category-icon"></i>
                        <span class="category-title">信号分析</span>
                        <i class="fas fa-chevron-down category-arrow"></i>
                    </div>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="fft-analysis.html" class="nav-link active" data-page="fft-analysis">
                                <i class="fas fa-chart-bar nav-icon"></i>
                                <span class="nav-text">频谱分析</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 意见反馈 - 底部固定位置 -->
            <div class="feedback-section">
                <a href="feedback.html" class="feedback-link">
                    <i class="fas fa-comment-dots feedback-icon"></i>
                    <span class="feedback-text">意见反馈</span>
                </a>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 内容布局 -->
            <div class="content-layout">
                <!-- 左侧控制面板 -->
                <div class="control-panel">
                    <!-- 数据导入卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-upload"></i>
                            数据导入
                        </h3>

                        <!-- 文件上传区域 -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">点击或拖拽文件到此处</div>
                            <div class="upload-hint">支持 Excel (.xlsx, .xls), CSV (.csv), TXT (.txt) 格式</div>
                            <input type="file" id="fileInput" accept=".xlsx,.xls,.csv,.txt" style="display: none;">
                        </div>

                        <!-- 直接粘贴数据区域 -->
                        <div class="form-group" style="margin-top: 20px;">
                            <label for="pasteData">或直接粘贴数据：</label>
                            <textarea id="pasteData" class="form-control" rows="4"
                                placeholder="粘贴数据，每行一个数值，或用逗号/制表符分隔"></textarea>
                        </div>

                        <!-- 数据预览 -->
                        <div id="dataPreview" class="data-preview" style="display: none;">
                            <table class="data-table" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>数值</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 15px;">
                            <button class="btn btn-outline" id="generateDemoBtn">
                                <i class="fas fa-magic"></i>
                                演示信号
                            </button>
                            <button class="btn btn-primary" id="loadDataBtn">
                                <i class="fas fa-check"></i>
                                加载数据
                            </button>
                        </div>
                    </div>

                    <!-- FFT参数设置卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-cogs"></i>
                            FFT参数设置
                        </h3>

                        <div class="form-group">
                            <label for="fftPoints">FFT点数：</label>
                            <select id="fftPoints" class="form-control form-select">
                                <option value="256">256</option>
                                <option value="512">512</option>
                                <option value="1024" selected>1024</option>
                                <option value="2048">2048</option>
                                <option value="4096">4096</option>
                                <option value="8192">8192</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sampleRate">采样频率 (Hz)：</label>
                            <input type="number" id="sampleRate" class="form-control" value="1000" min="1" max="100000">
                        </div>

                        <div class="form-group">
                            <label for="windowFunction">窗函数：</label>
                            <select id="windowFunction" class="form-control form-select">
                                <option value="rectangular">矩形窗</option>
                                <option value="hamming" selected>汉明窗</option>
                                <option value="hanning">汉宁窗</option>
                                <option value="blackman">布莱克曼窗</option>
                                <option value="kaiser">凯泽窗</option>
                                <option value="bartlett">巴特利特窗</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="overlapPercent">重叠百分比：</label>
                            <input type="range" id="overlapPercent" class="form-control" min="0" max="90" value="50" step="10">
                            <div style="text-align: center; margin-top: 5px; font-size: 12px; color: #64748b;">
                                <span id="overlapValue">50</span>%
                            </div>
                        </div>

                        <button class="btn btn-primary" id="performFFTBtn" style="width: 100%; margin-top: 15px;">
                            <i class="fas fa-play"></i>
                            执行FFT变换
                        </button>
                    </div>

                    <!-- 谐波分析卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-wave-square"></i>
                            谐波分析
                        </h3>

                        <div class="form-group">
                            <label for="fundamentalFreq">基频 (Hz)：</label>
                            <input type="number" id="fundamentalFreq" class="form-control" value="50" min="1" max="1000">
                        </div>

                        <div class="form-group">
                            <label for="harmonicOrder">谐波阶数：</label>
                            <select id="harmonicOrder" class="form-control form-select">
                                <option value="5">1-5次谐波</option>
                                <option value="9" selected>1-9次谐波</option>
                                <option value="15">1-15次谐波</option>
                                <option value="31">1-31次谐波</option>
                            </select>
                        </div>

                        <!-- 谐波结果显示 -->
                        <div id="harmonicResults" style="display: none; margin-top: 15px;">
                            <div style="font-size: 14px; font-weight: 600; margin-bottom: 10px; color: #1e293b;">谐波幅值：</div>
                            <div id="harmonicList" style="max-height: 150px; overflow-y: auto;"></div>

                            <div style="margin-top: 15px; padding: 10px; background: rgba(59, 130, 246, 0.1); border-radius: 8px;">
                                <div style="font-size: 14px; font-weight: 600; margin-bottom: 8px; color: #1e293b;">失真指标：</div>
                                <div style="font-size: 12px; color: #374151;">
                                    <div>THD: <span id="thdValue">-</span>%</div>
                                    <div>THD+N: <span id="thdnValue">-</span>%</div>
                                    <div>SINAD: <span id="sinadValue">-</span> dB</div>
                                </div>
                            </div>
                        </div>

                        <button class="btn btn-outline" id="analyzeHarmonicsBtn" style="width: 100%; margin-top: 15px;">
                            <i class="fas fa-calculator"></i>
                            分析谐波
                        </button>
                    </div>

                    <!-- 滤波器卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-filter"></i>
                            频域滤波
                        </h3>

                        <div class="form-group">
                            <label for="filterType">滤波器类型：</label>
                            <select id="filterType" class="form-control form-select">
                                <option value="lowpass">低通滤波器</option>
                                <option value="highpass">高通滤波器</option>
                                <option value="bandpass">带通滤波器</option>
                                <option value="bandstop">带阻滤波器</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="cutoffFreq1">截止频率1 (Hz)：</label>
                            <input type="number" id="cutoffFreq1" class="form-control" value="100" min="1">
                        </div>

                        <div class="form-group" id="cutoffFreq2Group" style="display: none;">
                            <label for="cutoffFreq2">截止频率2 (Hz)：</label>
                            <input type="number" id="cutoffFreq2" class="form-control" value="200" min="1">
                        </div>

                        <button class="btn btn-outline" id="applyFilterBtn" style="width: 100%; margin-top: 15px;">
                            <i class="fas fa-magic"></i>
                            应用滤波器
                        </button>
                    </div>

                    <!-- 导出功能卡片 -->
                    <div class="control-card">
                        <h3>
                            <i class="fas fa-download"></i>
                            数据导出
                        </h3>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <button class="btn btn-outline" id="exportDataBtn">
                                <i class="fas fa-file-csv"></i>
                                导出数据
                            </button>
                            <button class="btn btn-outline" id="exportImageBtn">
                                <i class="fas fa-image"></i>
                                导出图片
                            </button>
                        </div>

                        <button class="btn btn-outline" id="exportReportBtn" style="width: 100%; margin-top: 10px;">
                            <i class="fas fa-file-pdf"></i>
                            生成分析报告
                        </button>
                    </div>
                </div>

                <!-- 右侧图表区域 -->
                <div class="charts-area">
                    <!-- 时域信号图表 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                时域信号
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-outline" id="zoomResetTime" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-search-minus"></i>
                                    重置缩放
                                </button>
                                <button class="btn btn-outline" id="toggleTimeGrid" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-th"></i>
                                    网格
                                </button>
                            </div>
                        </div>
                        <div class="chart-canvas">
                            <canvas id="timeChart"></canvas>
                        </div>
                    </div>

                    <!-- FFT频谱图表 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                FFT频谱
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-outline" id="zoomResetFreq" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-search-minus"></i>
                                    重置缩放
                                </button>
                                <button class="btn btn-outline" id="toggleFreqGrid" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-th"></i>
                                    网格
                                </button>
                                <button class="btn btn-outline" id="markHarmonics" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-map-marker-alt"></i>
                                    标记谐波
                                </button>
                            </div>
                        </div>
                        <div class="chart-canvas large">
                            <canvas id="fftChart"></canvas>
                        </div>
                    </div>

                    <!-- 相位谱图表 -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-wave-square"></i>
                                相位谱
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-outline" id="zoomResetPhase" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-search-minus"></i>
                                    重置缩放
                                </button>
                                <button class="btn btn-outline" id="togglePhaseGrid" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-th"></i>
                                    网格
                                </button>
                            </div>
                        </div>
                        <div class="chart-canvas">
                            <canvas id="phaseChart"></canvas>
                        </div>
                    </div>

                    <!-- 滤波后信号对比 -->
                    <div class="chart-container" id="filteredContainer" style="display: none;">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-filter"></i>
                                滤波后信号对比
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-outline" id="zoomResetFiltered" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-search-minus"></i>
                                    重置缩放
                                </button>
                                <button class="btn btn-outline" id="toggleFilteredGrid" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-th"></i>
                                    网格
                                </button>
                            </div>
                        </div>
                        <div class="chart-canvas">
                            <canvas id="filteredChart"></canvas>
                        </div>
                    </div>

                    <!-- 谐波分析结果图表 -->
                    <div class="chart-container" id="harmonicContainer" style="display: none;">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                谐波分析结果
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-outline" id="toggleHarmonicView" style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-exchange-alt"></i>
                                    切换视图
                                </button>
                            </div>
                        </div>
                        <div class="chart-canvas">
                            <canvas id="harmonicChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 通知系统 -->
    <div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 10000;"></div>

    <!-- 滑动导航控件 -->
    <div class="slide-navigation">
        <div class="slide-nav-btn" id="slideUp" title="向上滑动图表">
            <i class="fas fa-chevron-up"></i>
        </div>
        <div class="slide-nav-btn" id="slideDown" title="向下滑动图表">
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>

    <!-- 页面指示器 -->
    <div class="page-indicator">
        <div class="page-dot active" data-section="time" title="时域图表"></div>
        <div class="page-dot" data-section="freq" title="频域图表"></div>
        <div class="page-dot" data-section="phase" title="相位图表"></div>
        <div class="page-dot" data-section="filtered" title="滤波图表"></div>
    </div>

    <!-- 固定水平滚动条 -->
    <div class="horizontal-scrollbar">
        <div class="horizontal-scrollbar-content"></div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/fft-analysis.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/background-effects.js"></script>
</body>
</html>