<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录错误测试</title>
    <link rel="stylesheet" href="css/toast-system.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>登录错误提示测试</h1>
        <p>点击下面的按钮测试不同类型的登录错误提示：</p>
        
        <button class="test-button" onclick="testCredentialError()">测试账号密码错误</button>
        <button class="test-button" onclick="testEmailNotFound()">测试邮箱未注册</button>
        <button class="test-button" onclick="testNetworkError()">测试网络错误</button>
        <button class="test-button" onclick="testValidationError()">测试输入验证错误</button>
        
        <h2>测试结果：</h2>
        <div id="test-results"></div>
    </div>

    <script src="js/toast-system.js"></script>
    <script>
        function testCredentialError() {
            if (window.showToast) {
                window.showToast('error', '登录失败', '账号或密码错误，请检查后重试', 5000);
                
                // 模拟2秒后刷新页面的提示
                setTimeout(() => {
                    document.getElementById('test-results').innerHTML += 
                        '<p>✅ 账号密码错误测试完成 - 2秒后会刷新页面（测试环境不执行刷新）</p>';
                }, 2000);
            } else {
                alert('Toast系统未加载');
            }
        }
        
        function testEmailNotFound() {
            if (window.showToast) {
                window.showToast('error', '登录失败', '该邮箱尚未注册，请先注册账号', 5000);
                document.getElementById('test-results').innerHTML += 
                    '<p>✅ 邮箱未注册错误测试完成</p>';
            } else {
                alert('Toast系统未加载');
            }
        }
        
        function testNetworkError() {
            if (window.showToast) {
                window.showToast('error', '登录失败', '网络连接异常，请检查网络后重试', 5000);
                document.getElementById('test-results').innerHTML += 
                    '<p>✅ 网络错误测试完成</p>';
            } else {
                alert('Toast系统未加载');
            }
        }
        
        function testValidationError() {
            if (window.showToast) {
                window.showToast('warning', '输入错误', '请输入邮箱地址', 3000);
                setTimeout(() => {
                    window.showToast('warning', '格式错误', '请输入有效的邮箱地址', 3000);
                }, 1000);
                setTimeout(() => {
                    window.showToast('warning', '输入错误', '请输入密码', 3000);
                }, 2000);
                setTimeout(() => {
                    window.showToast('warning', '格式错误', '密码至少需要6个字符', 3000);
                }, 3000);
                
                document.getElementById('test-results').innerHTML += 
                    '<p>✅ 输入验证错误测试完成</p>';
            } else {
                alert('Toast系统未加载');
            }
        }
        
        // 页面加载完成后检查toast系统
        document.addEventListener('DOMContentLoaded', function() {
            if (window.showToast) {
                document.getElementById('test-results').innerHTML = 
                    '<p>✅ Toast系统已正确加载</p>';
            } else {
                document.getElementById('test-results').innerHTML = 
                    '<p>❌ Toast系统未加载</p>';
            }
        });
    </script>
</body>
</html>
