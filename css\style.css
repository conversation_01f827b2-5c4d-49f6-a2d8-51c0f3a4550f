/* 全局样式 */
:root {
    --primary-color: #2c7be5;
    --primary-light: #5499ff;
    --primary-dark: #1a68d1;
    --secondary-color: #6e84a3;
    --success-color: #00d97e;
    --info-color: #39afd1;
    --warning-color: #f6c343;
    --danger-color: #e63757;
    --dark-color: #12263f;
    --light-color: #f9fbfd;
    --body-color: #f5f7fa;
    --body-bg: #fff;
    --border-color: #e3ebf6;
    --card-shadow: 0 .75rem 1.5rem rgba(18,38,63,.03);
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --gradient-primary: linear-gradient(135deg, var(--primary-color), #7c4dff);
    --gradient-light: linear-gradient(135deg, #f9fbfd, #e4ecfa);
    --glassmorphism-bg: rgba(255, 255, 255, 0.85);
    --glassmorphism-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
    --glassmorphism-border: 1px solid rgba(255, 255, 255, 0.18);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Nunito', "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #495057;
    background-color: var(--body-bg);
}

/* 登录页面样式 - 浅色调美感背景 */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    width: 100vw;
    background: linear-gradient(135deg,
        #f8fafc 0%,
        #e2e8f0 25%,
        #f1f5f9 50%,
        #e0f2fe 75%,
        #f0f9ff 100%);
    overflow: hidden;
    position: relative;
    margin: 0;
    padding: 0;
}

/* 几何粒子动效背景 */
.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 15% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 85% 75%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 45% 15%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 45%, rgba(245, 158, 11, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
    animation: backgroundShift 20s ease-in-out infinite;
}

/* 背景微动动画 */
@keyframes backgroundShift {
    0%, 100% {
        transform: translateX(0) translateY(0);
        opacity: 0.8;
    }
    25% {
        transform: translateX(10px) translateY(-5px);
        opacity: 1;
    }
    50% {
        transform: translateX(-5px) translateY(10px);
        opacity: 0.9;
    }
    75% {
        transform: translateX(5px) translateY(-8px);
        opacity: 0.95;
    }
}

/* 添加光晕效果层 */
.login-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.01) 50%, transparent 70%),
        radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.03) 0%, transparent 40%),
        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.02) 0%, transparent 40%);
    pointer-events: none;
    z-index: 1;
    animation: glowShift 15s ease-in-out infinite reverse;
}

/* 光晕移动动画 */
@keyframes glowShift {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: rotate(180deg) scale(1.05);
        opacity: 0.8;
    }
}

/* iconfont图标样式 */
.iconfont {
    font-size: 22px;
    height: 24px;
    width: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 现代化图标系统 */
.icon-menu, .icon-dashboard, .icon-analysis, .icon-chart, .icon-signal,
.icon-statistics, .icon-file, .icon-setting, .icon-help, .icon-user,
.icon-logout, .icon-add, .icon-notification, .icon-arrow-down, .icon-arrow-up,
.icon-arrow-right, .icon-upload, .icon-download, .icon-more, .icon-transform,
.icon-filter, .icon-window, .icon-time-axis, .icon-lock, .icon-eye, .icon-eye-close,
.icon-check, .icon-close, .icon-info, .icon-warning, .icon-wechat, .icon-qq, .icon-email {
    font-family: "FontAwesome", "iconfont" !important;
    font-style: normal;
    font-weight: normal;
    text-decoration: inherit;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 输入框图标样式 */
.input-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
    font-size: 18px;
    font-weight: normal;
    color: #8b9dc3;
    z-index: 2;
    pointer-events: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 2px 4px rgba(44, 123, 229, 0.1));
}

.icon-check::before {
    content: "✓"; /* 对勾图标 */
    font-family: Arial, sans-serif;
    font-weight: bold;
    color: #2ed573;
    font-size: 16px;
}

.icon-close::before {
    content: "✕"; /* 关闭图标 */
    font-family: Arial, sans-serif;
    font-weight: bold;
    color: #ff4757;
    font-size: 16px;
}

/* 现代化社交登录图标 */
.icon-wechat::before {
    content: "💬"; /* 微信图标 */
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
    font-size: 18px;
    font-weight: normal;
}

.icon-qq::before {
    content: "🐧"; /* QQ图标 */
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
    font-size: 18px;
    font-weight: normal;
}

.icon-email::before {
    content: "📧"; /* 邮箱图标 */
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
    font-size: 18px;
    font-weight: normal;
}





@keyframes iconGlow {
    0% {
        transform: translateY(-50%) scale(1.1);
        filter: drop-shadow(0 3px 6px rgba(44, 123, 229, 0.25));
        color: #2c7be5;
    }
    100% {
        transform: translateY(-50%) scale(1.15);
        filter: drop-shadow(0 4px 8px rgba(44, 123, 229, 0.35));
        color: #1e6bb8;
    }
}





/* 加载动画 */
.loading-spin {
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 数据分析主题增强粒子背景 */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(44, 123, 229, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 217, 255, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 123, 0, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, #f8faff 0%, #e6eefa 100%);
    animation: backgroundShift 20s ease infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        background:
            radial-gradient(circle at 20% 80%, rgba(44, 123, 229, 0.12) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(0, 217, 255, 0.12) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(255, 123, 0, 0.08) 0%, transparent 50%),
            linear-gradient(135deg, #f8faff 0%, #e6eefa 100%);
    }
    50% {
        background:
            radial-gradient(circle at 80% 20%, rgba(44, 123, 229, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 20% 80%, rgba(0, 217, 255, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(255, 123, 0, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, #f0f4ff 0%, #dde7fa 100%);
    }
}

/* 动态背景形状 - 现代化版本 */
.animated-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.animated-shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.3;
    filter: blur(25px);
    transition: all 0.7s ease;
}

.shape1 {
    width: 900px;
    height: 900px;
    background: radial-gradient(circle, rgba(83,120,255,0.5) 0%, rgba(0,119,255,0) 70%);
    bottom: -450px;
    left: -250px;
    animation: floatAnimation 25s infinite alternate;
}

.shape2 {
    width: 700px;
    height: 700px;
    background: radial-gradient(circle, rgba(0,217,255,0.5) 0%, rgba(0,217,255,0) 70%);
    top: -350px;
    right: -150px;
    animation: floatAnimation 30s infinite alternate-reverse;
}

.shape3 {
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(255,123,0,0.4) 0%, rgba(255,123,0,0) 70%);
    top: 60%;
    right: 12%;
    animation: floatAnimation 27s infinite alternate;
}

.shape4 {
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(255,0,144,0.4) 0%, rgba(255,0,144,0) 70%);
    bottom: 22%;
    left: 22%;
    animation: floatAnimation 23s infinite alternate-reverse;
}

@keyframes floatAnimation {
    0% {
        transform: translate(0, 0) scale(1) rotate(0deg);
    }
    33% {
        transform: translate(25px, 15px) scale(1.05) rotate(3deg);
    }
    66% {
        transform: translate(15px, -15px) scale(0.95) rotate(7deg);
    }
    100% {
        transform: translate(-20px, -15px) scale(1.02) rotate(-3deg);
    }
}

/* 装饰元素样式 - 更现代化 */
.decoration-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.floating-circle {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 3px solid rgba(44, 123, 229, 0.25);
    top: 15%;
    left: 18%;
    animation: float 18s infinite ease-in-out;
}

.floating-square {
    position: absolute;
    width: 80px;
    height: 80px;
    border: 3px solid rgba(0, 217, 255, 0.25);
    bottom: 25%;
    right: 15%;
    transform: rotate(45deg);
    animation: float 22s infinite ease-in-out reverse;
}

.floating-triangle {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 50px solid transparent;
    border-right: 50px solid transparent;
    border-bottom: 85px solid rgba(255, 123, 0, 0.15);
    top: 28%;
    right: 25%;
    animation: float 24s infinite ease-in-out;
}

.floating-dots {
    position: absolute;
    width: 150px;
    height: 150px;
    bottom: 18%;
    left: 23%;
    background-image: radial-gradient(circle, rgba(124, 77, 255, 0.25) 3px, transparent 3px);
    background-size: 18px 18px;
    animation: float 28s infinite ease-in-out reverse;
}

@keyframes float {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    25% {
        transform: translate(15px, 20px) rotate(7deg);
    }
    50% {
        transform: translate(8px, -15px) rotate(-7deg);
    }
    75% {
        transform: translate(-15px, 8px) rotate(5deg);
    }
    100% {
        transform: translate(0, 0) rotate(0deg);
    }
}



/* 浅色调几何粒子动效系统 */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
}

/* 几何粒子基础样式 */
.particle {
    position: absolute;
    animation: geometricFloat 20s linear infinite;
    opacity: 0.7;
}

/* 圆形粒子 */
.particle1 {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.6);
}

.particle4 {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(139, 92, 246, 0.5);
}

.particle7 {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(16, 185, 129, 0.6);
}

.particle10 {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: rgba(245, 158, 11, 0.5);
}

.particle13 {
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: rgba(236, 72, 153, 0.6);
}

/* 方形粒子 */
.particle2 {
    width: 6px;
    height: 6px;
    background: rgba(59, 130, 246, 0.5);
    transform: rotate(45deg);
}

.particle5 {
    width: 8px;
    height: 8px;
    background: rgba(139, 92, 246, 0.4);
    transform: rotate(45deg);
}

.particle8 {
    width: 5px;
    height: 5px;
    background: rgba(16, 185, 129, 0.5);
    transform: rotate(45deg);
}

.particle11 {
    width: 7px;
    height: 7px;
    background: rgba(245, 158, 11, 0.4);
    transform: rotate(45deg);
}

.particle14 {
    width: 6px;
    height: 6px;
    background: rgba(236, 72, 153, 0.5);
    transform: rotate(45deg);
}

/* 三角形粒子 */
.particle3 {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid rgba(59, 130, 246, 0.5);
}

.particle6 {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 8px solid rgba(139, 92, 246, 0.4);
}

.particle9 {
    width: 0;
    height: 0;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 12px solid rgba(16, 185, 129, 0.5);
}

.particle12 {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 7px solid rgba(245, 158, 11, 0.4);
}

.particle15 {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 9px solid rgba(236, 72, 153, 0.5);
}

/* 粒子位置和延迟 */
.particle1 { left: 10%; bottom: -20px; animation-delay: 0s; }
.particle2 { left: 25%; bottom: -20px; animation-delay: 2s; }
.particle3 { left: 40%; bottom: -20px; animation-delay: 4s; }
.particle4 { left: 55%; bottom: -20px; animation-delay: 6s; }
.particle5 { left: 70%; bottom: -20px; animation-delay: 8s; }
.particle6 { left: 85%; bottom: -20px; animation-delay: 10s; }
.particle7 { left: 15%; bottom: -20px; animation-delay: 12s; }
.particle8 { left: 30%; bottom: -20px; animation-delay: 14s; }
.particle9 { left: 45%; bottom: -20px; animation-delay: 16s; }
.particle10 { left: 60%; bottom: -20px; animation-delay: 18s; }
.particle11 { left: 75%; bottom: -20px; animation-delay: 1s; }
.particle12 { left: 90%; bottom: -20px; animation-delay: 3s; }
.particle13 { left: 20%; bottom: -20px; animation-delay: 5s; }
.particle14 { left: 35%; bottom: -20px; animation-delay: 7s; }
.particle15 { left: 80%; bottom: -20px; animation-delay: 9s; }

/* 几何粒子浮动动画 */
@keyframes geometricFloat {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    90% {
        opacity: 0.8;
    }
    100% {
        transform: translateY(-120vh) rotate(360deg);
        opacity: 0;
    }
}

/* 星星点缀效果 */
.stars-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
}

.star {
    position: absolute;
    font-size: 12px;
    color: rgba(59, 130, 246, 0.6);
    animation: starTwinkle 3s ease-in-out infinite;
    font-family: Arial, sans-serif;
}

/* 星星闪烁动画 */
@keyframes starTwinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* 星星随机位置 */
.star1 { top: 15%; left: 12%; animation-delay: 0s; color: rgba(59, 130, 246, 0.7); }
.star2 { top: 25%; left: 78%; animation-delay: 0.5s; color: rgba(139, 92, 246, 0.6); }
.star3 { top: 35%; left: 25%; animation-delay: 1s; color: rgba(16, 185, 129, 0.7); }
.star4 { top: 45%; left: 85%; animation-delay: 1.5s; color: rgba(245, 158, 11, 0.6); }
.star5 { top: 55%; left: 15%; animation-delay: 2s; color: rgba(236, 72, 153, 0.7); }
.star6 { top: 65%; left: 65%; animation-delay: 2.5s; color: rgba(59, 130, 246, 0.6); }
.star7 { top: 75%; left: 35%; animation-delay: 0.3s; color: rgba(139, 92, 246, 0.7); }
.star8 { top: 85%; left: 90%; animation-delay: 0.8s; color: rgba(16, 185, 129, 0.6); }
.star9 { top: 20%; left: 50%; animation-delay: 1.3s; color: rgba(245, 158, 11, 0.7); }
.star10 { top: 40%; left: 5%; animation-delay: 1.8s; color: rgba(236, 72, 153, 0.6); }
.star11 { top: 60%; left: 95%; animation-delay: 2.3s; color: rgba(59, 130, 246, 0.7); }
.star12 { top: 80%; left: 8%; animation-delay: 0.1s; color: rgba(139, 92, 246, 0.6); }

/* 随机几何图形装饰系统 */
.geometric-decorations {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
}

.geo-shape {
    position: absolute;
    animation: gentleFloat 25s ease-in-out infinite;
}

/* 圆形装饰 */
.circle-deco {
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        rgba(59, 130, 246, 0.15),
        rgba(59, 130, 246, 0.05),
        transparent 70%);
}

.c1 { width: 60px; height: 60px; top: 18%; left: 8%; animation-delay: 0s; }
.c2 { width: 40px; height: 40px; top: 72%; left: 85%; animation-delay: 8s; }
.c3 { width: 80px; height: 80px; top: 45%; left: 92%; animation-delay: 16s; }
.c4 { width: 35px; height: 35px; top: 88%; left: 12%; animation-delay: 24s; }

/* 方形装饰 */
.square-deco {
    background: linear-gradient(45deg,
        rgba(139, 92, 246, 0.12),
        rgba(139, 92, 246, 0.04));
    transform: rotate(45deg);
}

.s1 { width: 25px; height: 25px; top: 28%; left: 75%; animation-delay: 4s; }
.s2 { width: 35px; height: 35px; top: 65%; left: 5%; animation-delay: 12s; }
.s3 { width: 20px; height: 20px; top: 15%; left: 88%; animation-delay: 20s; }

/* 三角形装饰 */
.triangle-deco {
    width: 0;
    height: 0;
}

.t1 {
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid rgba(16, 185, 129, 0.12);
    top: 38%; left: 15%; animation-delay: 6s;
}

.t2 {
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 20px solid rgba(245, 158, 11, 0.12);
    top: 78%; left: 65%; animation-delay: 14s;
}

.t3 {
    border-left: 18px solid transparent;
    border-right: 18px solid transparent;
    border-bottom: 30px solid rgba(236, 72, 153, 0.12);
    top: 25%; left: 45%; animation-delay: 22s;
}

/* 六边形装饰 */
.hexagon-deco {
    width: 30px;
    height: 26px;
    background: rgba(99, 102, 241, 0.1);
    position: relative;
}

.hexagon-deco::before,
.hexagon-deco::after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
}

.hexagon-deco::before {
    bottom: 100%;
    border-bottom: 8px solid rgba(99, 102, 241, 0.1);
}

.hexagon-deco::after {
    top: 100%;
    border-top: 8px solid rgba(99, 102, 241, 0.1);
}

.h1 { top: 52%; left: 25%; animation-delay: 10s; }
.h2 { top: 82%; left: 78%; animation-delay: 18s; }

/* 温和浮动动画 */
@keyframes gentleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(15px) rotate(90deg);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-10px) translateX(-20px) rotate(180deg);
        opacity: 0.4;
    }
    75% {
        transform: translateY(15px) translateX(10px) rotate(270deg);
        opacity: 0.7;
    }
}

/* 装饰几何图形 - 保留原有的大型装饰 */
.decoration-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        rgba(59, 130, 246, 0.08),
        rgba(59, 130, 246, 0.02),
        transparent 70%);
    animation: gentleFloat 35s ease-in-out infinite;
    width: 150px;
    height: 150px;
    top: 10%;
    left: 3%;
}

.floating-square {
    position: absolute;
    background: linear-gradient(45deg,
        rgba(139, 92, 246, 0.06),
        rgba(139, 92, 246, 0.01));
    animation: gentleFloat 40s ease-in-out infinite;
    transform: rotate(45deg);
    width: 100px;
    height: 100px;
    top: 75%;
    right: 8%;
    animation-delay: 15s;
}

.floating-triangle {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 50px solid transparent;
    border-right: 50px solid transparent;
    border-bottom: 75px solid rgba(16, 185, 129, 0.06);
    animation: gentleFloat 45s ease-in-out infinite;
    top: 35%;
    right: 5%;
    animation-delay: 25s;
}

.floating-dots {
    position: absolute;
    width: 120px;
    height: 120px;
    background: radial-gradient(circle at 50% 50%,
        rgba(245, 158, 11, 0.1) 2px,
        transparent 2px);
    background-size: 20px 20px;
    animation: gentleFloat 50s ease-in-out infinite;
    opacity: 0.3;
    top: 55%;
    left: 2%;
    animation-delay: 35s;
}

/* 登录容器样式增强 - 数据分析主题玻璃态设计 */
.login-container {
    position: relative;
    z-index: 2;
    width: 450px;
    max-width: 90vw;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.95),
        rgba(255, 255, 255, 0.85));
    border-radius: 28px;
    padding: 35px;
    box-shadow:
        0 25px 50px rgba(44, 123, 229, 0.15),
        0 15px 35px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    opacity: 1;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: containerEntrance 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    margin: auto;
    overflow: hidden;
}

/* 数据分析主题的容器入场动画 */
@keyframes containerEntrance {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
        filter: blur(10px);
    }
    60% {
        opacity: 0.8;
        transform: translateY(-10px) scale(1.02);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数据分析主题的悬停效果 */
.login-container:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(44, 123, 229, 0.25),
        0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(44, 123, 229, 0.3);
}

/* 数据可视化主题装饰元素 */
.login-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(44, 123, 229, 0.4),
        rgba(0, 217, 255, 0.4),
        rgba(255, 123, 0, 0.4),
        rgba(255, 0, 144, 0.4));
    background-size: 400% 400%;
    border-radius: 30px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.6s ease;
    animation: gradientShift 8s ease infinite;
}

.login-container:hover::before {
    opacity: 0.7;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 数据图表装饰线条 */
.login-container::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><path d="M10 50 L20 30 L30 40 L40 20 L50 35" stroke="rgba(44,123,229,0.2)" stroke-width="2" fill="none"/><circle cx="20" cy="30" r="2" fill="rgba(44,123,229,0.3)"/><circle cx="30" cy="40" r="2" fill="rgba(44,123,229,0.3)"/><circle cx="40" cy="20" r="2" fill="rgba(44,123,229,0.3)"/><circle cx="50" cy="35" r="2" fill="rgba(44,123,229,0.3)"/></svg>') no-repeat center;
    opacity: 0.1;
    transition: opacity 0.3s ease;
}

.login-container:hover::after {
    opacity: 0.3;
}

.login-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 35px;
    animation: fadeIn 0.8s 0.3s both;
    position: relative;
    z-index: 1;
}

.logo-wrapper {
    width: 110px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4));
    border-radius: 50%;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    border: var(--glassmorphism-border);
}

.logo-wrapper::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    transform: rotate(45deg);
    animation: shineEffect 3s infinite;
}

@keyframes shineEffect {
    0% { transform: translateX(-100%) rotate(45deg); }
    50%, 100% { transform: translateX(100%) rotate(45deg); }
}

/* 数据分析主题Logo样式 */
.logo {
    width: 75px;
    height: 75px;
    filter: drop-shadow(0 5px 15px rgba(44, 123, 229, 0.2));
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 1;
}

.data-analytics-logo svg {
    width: 100%;
    height: 100%;
    animation: logoGlow 4s ease infinite alternate;
}

@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 5px 15px rgba(44, 123, 229, 0.2));
    }
    100% {
        filter: drop-shadow(0 8px 25px rgba(44, 123, 229, 0.4));
    }
}

.login-container:hover .logo {
    transform: translateY(-8px) scale(1.1) rotate(5deg);
    filter: drop-shadow(0 12px 30px rgba(44, 123, 229, 0.5));
}

.login-container:hover .data-analytics-logo svg {
    animation: logoHover 2s ease infinite;
}

@keyframes logoHover {
    0%, 100% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.05) rotate(2deg);
    }
}

.login-header h1 {
    color: var(--dark-color);
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    letter-spacing: 0.5px;
}

.login-subtitle {
    color: var(--secondary-color);
    font-size: 17px;
    text-align: center;
    margin-top: 5px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.header-divider {
    width: 70px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
    margin-top: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* 登录表单样式优化 */
.login-form {
    opacity: 0;
    animation: fadeIn 0.8s 0.6s forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(15px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 增强输入框样式 */
.input-group {
    position: relative;
    margin-bottom: 32px;
}

/* 输入框图标聚焦效果 */
.input-group:focus-within .input-icon,
.input-group input:focus + .input-icon {
    color: #2c7be5;
    transform: translateY(-50%) scale(1.1);
    filter: drop-shadow(0 3px 6px rgba(44, 123, 229, 0.25));
    animation: iconGlow 2s ease infinite alternate;
}

@keyframes iconGlow {
    0% {
        transform: translateY(-50%) scale(1.1);
        filter: drop-shadow(0 3px 6px rgba(44, 123, 229, 0.25));
    }
    100% {
        transform: translateY(-50%) scale(1.15);
        filter: drop-shadow(0 4px 8px rgba(44, 123, 229, 0.35));
    }
}

.input-group input {
    width: 100%;
    padding: 16px 45px 16px 50px;
    border: 2px solid rgba(44, 123, 229, 0.3);
    border-radius: 14px;
    font-size: 16px;
    transition: var(--transition-normal);
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), #7c4dff);
    transition: width 0.4s ease-out;
    opacity: 0;
    border-radius: 0 0 10px 10px;
}

.input-group input:focus ~ .input-focus-border {
    width: 100%;
    opacity: 1;
}

/* 数据分析主题的输入框聚焦效果 */
.input-group input:focus {
    border-color: var(--primary-color);
    box-shadow:
        0 8px 25px rgba(44, 123, 229, 0.25),
        0 0 0 4px rgba(44, 123, 229, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        0 0 20px rgba(44, 123, 229, 0.1);
    outline: none;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.98),
        rgba(248, 250, 255, 0.95));
    transform: translateY(-4px) scale(1.03);
    animation: inputGlow 2s ease infinite alternate;
}

@keyframes inputGlow {
    0% {
        box-shadow:
            0 8px 25px rgba(44, 123, 229, 0.25),
            0 0 0 4px rgba(44, 123, 229, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            0 0 20px rgba(44, 123, 229, 0.1);
    }
    100% {
        box-shadow:
            0 8px 25px rgba(44, 123, 229, 0.35),
            0 0 0 4px rgba(44, 123, 229, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            0 0 30px rgba(44, 123, 229, 0.2);
    }
}





/* 表单选项样式 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 5px;
}

.forgot-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 15px;
    transition: var(--transition-normal);
    position: relative;
    padding-bottom: 2px;
}

.forgot-link:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.forgot-link:hover {
    color: var(--primary-dark);
}

.forgot-link:hover:after {
    width: 100%;
}

/* 记住我复选框样式增强 */
.custom-checkbox {
    display: inline-flex;
    align-items: center;
    position: relative;
    padding-left: 32px;
    cursor: pointer;
    user-select: none;
    color: var(--secondary-color);
    font-size: 15px;
}

.custom-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.custom-checkbox::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.7);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    transition: var(--transition-normal);
}

.custom-checkbox::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 2px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg) translateY(2px);
    opacity: 0;
    transition: var(--transition-normal);
}

/* 修复复选框选中状态 */
.custom-checkbox input:checked + .checkmark::before,
.custom-checkbox input:checked ~ .checkmark::before {
    background: linear-gradient(135deg, #2c7be5, #00d9ff) !important;
    border-color: #2c7be5 !important;
    box-shadow: 0 2px 8px rgba(44, 123, 229, 0.4) !important;
}

.custom-checkbox input:checked + .checkmark::after,
.custom-checkbox input:checked ~ .checkmark::after {
    opacity: 1 !important;
}

/* 直接针对父元素的伪元素 */
.custom-checkbox:has(input:checked)::before {
    background: linear-gradient(135deg, #2c7be5, #00d9ff) !important;
    border-color: #2c7be5 !important;
    box-shadow: 0 2px 8px rgba(44, 123, 229, 0.4) !important;
}

.custom-checkbox:has(input:checked)::after {
    opacity: 1 !important;
}

/* JavaScript控制的复选框状态 */
.custom-checkbox.checked::before {
    background: linear-gradient(135deg, #2c7be5, #00d9ff) !important;
    border-color: #2c7be5 !important;
    box-shadow: 0 2px 8px rgba(44, 123, 229, 0.4) !important;
}

.custom-checkbox.checked::after {
    opacity: 1 !important;
}

/* 功能亮点部分 */
.features-highlights {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
    border-top: 1px solid var(--border-color);
    padding-top: 25px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 5px 15px rgba(44, 123, 229, 0.25);
    transition: var(--transition-normal);
}

.feature:hover .feature-icon {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(44, 123, 229, 0.35);
}

.feature-text {
    font-size: 14px;
    font-weight: 600;
    color: var(--dark-color);
}

/* 按钮样式增强 */
.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: .8rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 14px;
    transition: var(--transition-normal);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.login-btn {
    display: block;
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color), #7c4dff);
    color: white;
    margin-bottom: 25px;
    font-size: 17px;
    letter-spacing: 0.7px;
    padding: 16px 25px;
    box-shadow: 0 8px 20px rgba(44, 123, 229, 0.35);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    font-weight: 700;
}

.login-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.login-btn:hover {
    background: linear-gradient(135deg, #6c63ff, var(--primary-light));
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(44, 123, 229, 0.5);
}

.login-btn:hover:before {
    left: 100%;
}

.login-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 12px rgba(44, 123, 229, 0.4);
}

.btn-loader {
    display: none;
    position: absolute;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.btn-loading .btn-text {
    visibility: hidden;
}

.btn-loading .btn-loader {
    display: flex;
}

.loader-dot {
    width: 8px;
    height: 8px;
    margin: 0 3px;
    background-color: white;
    border-radius: 50%;
    display: inline-block;
    animation: dotPulse 1.4s infinite ease-in-out both;
}

.loader-dot:nth-child(1) {
    animation-delay: 0s;
}

.loader-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loader-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes dotPulse {
    0%, 80%, 100% { 
        transform: scale(0);
        opacity: 0.5;
    } 
    40% { 
        transform: scale(1.0);
        opacity: 1;
    }
}

/* 注册链接样式增强 */
.register-link {
    text-align: center;
    color: var(--secondary-color);
    margin-bottom: 20px;
    font-size: 15px;
}

.register-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    position: relative;
}

.register-link a:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.register-link a:hover {
    color: var(--primary-dark);
}

.register-link a:hover:after {
    transform: scaleX(1);
    transform-origin: left;
}

/* 直接访问链接样式 */
.direct-access {
    text-align: center;
    margin-top: 15px;
    margin-bottom: 20px;
}

.direct-link {
    display: inline-block;
    padding: 10px 20px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.direct-link:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    color: white;
}

/* 社交登录部分 */
.social-login {
    text-align: center;
    padding-top: 25px;
    border-top: 1px solid var(--border-color);
    margin-top: 15px;
}

.social-login p {
    font-size: 14px;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: none !important;
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition-normal);
    box-shadow: none !important;
    border: none !important;
}

.social-icon span {
    font-size: 20px;
}

.social-icon:hover {
    background: none !important;
    color: var(--primary-color);
    transform: translateY(-3px) scale(1.15);
    filter: drop-shadow(0 4px 8px rgba(44, 123, 229, 0.3));
}

.copyright {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    text-align: center;
    color: var(--secondary-color);
    font-size: 12px;
    z-index: 10;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    line-height: 1.4;
    padding: 0 20px;
}

.copyright:hover {
    opacity: 1;
}

/* 数据分析主题消息提示框样式 */
.message-box {
    position: fixed;
    top: 25px;
    right: 25px;
    z-index: 1000;
    max-width: 400px;
    perspective: 1000px;
}

/* 数据分析主题通知样式 */
.alert {
    padding: 18px 24px;
    margin-bottom: 15px;
    border-radius: 16px;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.15),
        0 8px 20px rgba(44, 123, 229, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    color: white;
    opacity: 0;
    transform: translateX(50px) rotateY(15deg);
    transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 320px;
    overflow: hidden;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 数据图表风格的装饰线条 */
.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.8),
        rgba(255, 255, 255, 0.4));
    animation: dataFlow 2s ease infinite;
}

@keyframes dataFlow {
    0%, 100% {
        background: linear-gradient(180deg,
            rgba(255, 255, 255, 0.8),
            rgba(255, 255, 255, 0.4));
    }
    50% {
        background: linear-gradient(180deg,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.6));
    }
}

/* 数据分析主题通知显示状态 */
.alert.show {
    opacity: 1;
    transform: translateX(0) rotateY(0deg);
    animation: notificationEntrance 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes notificationEntrance {
    0% {
        opacity: 0;
        transform: translateX(50px) rotateY(15deg) scale(0.9);
        filter: blur(5px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(-5px) rotateY(-2deg) scale(1.02);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) rotateY(0deg) scale(1);
        filter: blur(0);
    }
}

/* 数据分析主题通知类型样式 */
.alert.info {
    background: linear-gradient(135deg, #2c7be5, #00d9ff);
}

.alert.success {
    background: linear-gradient(135deg, #2ed573, #1dd1a1);
}

.alert.error {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.alert.warning {
    background: linear-gradient(135deg, #ff7b00, #ff6b35);
}

/* 通知图标样式 */
.alert .alert-icon {
    font-size: 20px;
    opacity: 0.9;
    animation: iconPulse 2s ease infinite;
}

@keyframes iconPulse {
    0%, 100% {
        opacity: 0.9;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.alert.warning {
    background-color: var(--warning-color);
}

.alert.fade-out {
    opacity: 0;
    transform: translateX(30px);
}

.alert .alert-icon {
    font-size: 20px;
}

.alert .message-text {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
}

.alert .close-btn {
    background: none;
    border: none;
    color: white;
    opacity: 0.7;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s, transform 0.3s;
}

.alert .close-btn:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* 输入框焦点动画 */
.input-group.focused .iconfont {
    color: var(--primary-color);
}

/* 输入动画 */
@keyframes shake {
    0%, 100% {transform: translateX(0);}
    10%, 30%, 50%, 70%, 90% {transform: translateX(-5px);}
    20%, 40%, 60%, 80% {transform: translateX(5px);}
}

.shake-animation {
    animation: shake 0.5s;
}

/* 隐藏浏览器默认验证提示 */
input:invalid {
    box-shadow: none !important;
}

input::-webkit-validation-bubble,
input::-webkit-validation-bubble-message,
input::-webkit-validation-bubble-arrow,
input::-webkit-validation-bubble-top-outer-arrow,
input::-webkit-validation-bubble-top-inner-arrow {
    display: none !important;
}

/* 数据分析主题表单验证消息 - 增强版 */
.validation-message {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    margin-top: 8px;
    display: none;
    position: absolute;
    bottom: -55px;
    left: -8px;
    right: -8px;
    transform: translateY(-25px) scale(0.8);
    opacity: 0;
    transition: all 0.9s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: none;
    background: linear-gradient(135deg,
        #ff6b6b 0%,
        #ff5252 30%,
        #f44336 70%,
        #e53935 100%);
    padding: 16px 24px 16px 50px;
    border-radius: 24px;
    backdrop-filter: blur(25px);
    box-shadow:
        0 20px 50px rgba(255, 107, 107, 0.5),
        0 12px 25px rgba(255, 107, 107, 0.4),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(0, 0, 0, 0.15);
    z-index: 20;
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    letter-spacing: 0.5px;
    text-align: left;
    position: relative;
    overflow: hidden;
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 数据流动背景效果 */
.validation-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: dataFlowValidation 3s ease infinite;
}

@keyframes dataFlowValidation {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 数据分析主题验证消息图标 */
.validation-message {
    position: relative;
}

.validation-message::after {
    content: '⚠️';
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: rgba(255, 255, 255, 0.98);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
    animation: warningDataPulse 3s ease infinite;
    z-index: 3;
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

/* 数据分析主题箭头指示器 */
.validation-message:before {
    content: '';
    position: absolute;
    top: -12px;
    left: 30px;
    width: 0;
    height: 0;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-bottom: 12px solid #ff6b6b;
    filter: drop-shadow(0 -3px 6px rgba(255, 107, 107, 0.4));
    z-index: 2;
}

@keyframes warningDataPulse {
    0%, 100% {
        opacity: 0.95;
        transform: translateY(-50%) scale(1);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.2);
        text-shadow: 0 3px 8px rgba(0, 0, 0, 0.6), 0 0 15px rgba(255, 255, 255, 0.3);
    }
}

.input-error {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 3px rgba(230, 55, 87, 0.2) !important;
}

.input-error + .iconfont,
.input-error + .icon {
    color: var(--danger-color) !important;
    animation: pulse-icon 0.6s ease;
}

@keyframes pulse-icon {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* 数据分析主题验证消息显示动画 */
.input-error ~ .validation-message {
    display: block;
    transform: translateY(0) scale(1);
    opacity: 1;
    animation: validationSlideIn 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes validationSlideIn {
    0% {
        transform: translateY(-25px) scale(0.8);
        opacity: 0;
        filter: blur(5px);
    }
    60% {
        transform: translateY(3px) scale(1.05);
        opacity: 0.9;
        filter: blur(1px);
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
        filter: blur(0);
    }
}

/* 数据分析主题成功验证消息样式 */
.validation-message.success {
    background: linear-gradient(135deg, #2ed573 0%, #1dd1a1 50%, #00c851 100%);
    color: #ffffff;
    box-shadow:
        0 15px 40px rgba(46, 213, 115, 0.4),
        0 8px 20px rgba(46, 213, 115, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.validation-message.success:before {
    border-bottom-color: #2ed573;
}

.validation-message.success::after {
    content: '✓';
    color: rgba(255, 255, 255, 0.98);
    font-size: 20px;
    font-weight: bold;
    animation: successDataPulse 2s ease infinite;
}

@keyframes successDataPulse {
    0%, 100% {
        opacity: 0.98;
        transform: translateY(-50%) scale(1);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.3);
        text-shadow: 0 3px 8px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.4);
    }
}

/* 动态反馈提示 */
.feedback-tip {
    position: absolute;
    background-color: white;
    padding: 8px 12px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    transition: all 0.3s ease;
    pointer-events: none;
    opacity: 0;
    transform: translateY(10px);
    z-index: 100;
}

.feedback-tip.success {
    color: var(--success-color);
    border-left: 3px solid var(--success-color);
}

.feedback-tip.error {
    color: var(--danger-color);
    border-left: 3px solid var(--danger-color);
}

.feedback-tip.info {
    color: var(--info-color);
    border-left: 3px solid var(--info-color);
}

.feedback-tip.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 表单验证检查标记 */
.input-group {
    position: relative;
}

.validation-icon {
    position: absolute;
    right: 15px;
    top: 16px;
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none;
    z-index: 5;
}

/* 密码输入框的验证图标需要避开密码切换按钮 */
.input-group:has(input[type="password"]) .validation-icon {
    right: 55px;
}

/* 如果浏览器不支持:has()选择器，使用备用方案 */
.input-group .validation-icon {
    right: 15px;
}



/* 更强力的选择器 - 针对所有可能的验证图标 */
input[type="password"] ~ .validation-icon,
input[type="password"] ~ .validation-icon.show,
input[type="password"] ~ .validation-icon.success,
input[type="password"] ~ .validation-icon.error,
input[id="password"] ~ .validation-icon,
input[id="password"] ~ .validation-icon.show,
input[id="password"] ~ .validation-icon.success,
input[id="password"] ~ .validation-icon.error {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* 通过父容器隐藏 */
.input-group:has(input[type="password"]) .validation-icon,
.input-group:has(input[id="password"]) .validation-icon {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* 最终解决方案：直接针对密码输入框后的所有验证图标 */
#password ~ .validation-icon,
#password ~ .validation-icon.success,
#password ~ .validation-icon.error,
#password ~ .validation-icon.show {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    pointer-events: none !important;
}

/* 确保所有可能的验证图标都被隐藏 */
.input-group .validation-icon.success,
.input-group .validation-icon.error,
.input-group .validation-icon.show,
.input-group span.validation-icon {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

.validation-icon.success {
    color: var(--success-color);
}

.validation-icon.error {
    color: var(--danger-color);
}

.input-validated .validation-icon {
    opacity: 1;
    animation: fadeInRight 0.3s forwards;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 密码强度指示器 */
.password-strength {
    height: 4px;
    width: 100%;
    background-color: #eee;
    border-radius: 2px;
    margin-top: 8px;
    position: relative;
    display: none;
}

.password-strength-bar {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease, background-color 0.3s ease;
    width: 0%;
}

.password-strength-text {
    font-size: 11px;
    margin-top: 5px;
    display: none;
    position: absolute;
    right: 0;
    font-weight: 600;
}

.password-strength.weak .password-strength-bar {
    width: 25%;
    background-color: #ff4747;
}

.password-strength.medium .password-strength-bar {
    width: 50%;
    background-color: #ffac47;
}

.password-strength.good .password-strength-bar {
    width: 75%;
    background-color: #2dbf56;
}

.password-strength.strong .password-strength-bar {
    width: 100%;
    background-color: #0acf83;
}

.password-strength.show,
.password-strength-text.show {
    display: block;
}

.password-strength-text.weak {
    color: #ff4747;
}

.password-strength-text.medium {
    color: #ffac47;
}

.password-strength-text.good {
    color: #2dbf56;
}

.password-strength-text.strong {
    color: #0acf83;
}

/* 表单字段聚焦和填写状态 */
.input-group.filled input {
    background-color: rgba(255, 255, 255, 0.8);
}

.input-group.filled label {
    transform: translateY(-20px) scale(0.85);
    color: var(--primary-color);
}

.input-group.error input {
    border-color: var(--danger-color);
    background-color: rgba(230, 55, 87, 0.05);
}

.input-group.success input {
    border-color: var(--success-color);
    background-color: rgba(0, 217, 126, 0.05);
}

/* 页面加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.98);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.5s, visibility 0.5s;
}

.page-loader.loaded {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(44, 123, 229, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s infinite linear;
    margin-bottom: 15px;
}

.page-loader p {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 1px;
    margin: 0;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .login-container {
        width: 90%;
        max-width: 450px;
        padding: 35px 30px;
        margin: 20px;
        border-radius: 20px;
    }
    
    .features-highlights {
        flex-wrap: wrap;
        justify-content: center;
        gap: 25px;
    }
    
    .feature {
        flex-basis: 45%;
        min-width: 120px;
    }
    
    .animated-shape {
        opacity: 0.2;
    }
    
    .login-header h1 {
        font-size: 32px;
    }
    
    .logo-wrapper {
        width: 100px;
        height: 100px;
    }
    
    .logo {
        width: 65px;
        height: 65px;
    }
    
    .copyright {
        font-size: 12px;
        padding: 0 20px;
    }
}

@media (max-width: 576px) {
    .login-container {
        width: 92%;
        padding: 30px 25px;
        margin: 15px;
    }

    .login-header h1 {
        font-size: 28px;
    }
    
    .login-subtitle {
        font-size: 15px;
    }
    
    .logo-wrapper {
        width: 90px;
        height: 90px;
        margin-bottom: 20px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
    }
    
    .feature-icon {
        width: 45px;
        height: 45px;
    }
    
    .feature-text {
        font-size: 13px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .forgot-link {
        align-self: flex-end;
    }
    
    .login-btn {
        font-size: 16px;
        padding: 14px 20px;
    }
    
    .social-icon {
        width: 40px;
        height: 40px;
    }
    
    .floating-circle, 
    .floating-square, 
    .floating-triangle, 
    .floating-dots {
        transform: scale(0.7);
    }
    
    .particles-container {
        opacity: 0.7;
    }
}

@media (max-width: 480px) {
    .login-container {
        width: 94%;
        padding: 25px 20px;
    }

    .login-header h1 {
        font-size: 26px;
    }
    
    .login-subtitle {
        font-size: 14px;
    }
    
    .logo-wrapper {
        width: 80px;
        height: 80px;
    }
    
    .logo {
        width: 55px;
        height: 55px;
    }
    
    .features-highlights {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .feature {
        flex-basis: 30%;
        min-width: 80px;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
    }
    
    .feature-text {
        font-size: 12px;
    }
    
    .input-group input {
        padding: 14px 40px 14px 45px;
        font-size: 15px;
    }
    
    .input-group .iconfont,
    .input-group .icon {
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
    }
    
    .custom-checkbox {
        font-size: 14px;
    }
    
    .social-login p {
        font-size: 13px;
    }
}

@media (max-height: 700px) {
    .login-container {
        padding: 25px;
        margin: 10px;
    }
    
    .logo-wrapper {
        width: 80px;
        height: 80px;
        margin-bottom: 15px;
    }
    
    .logo {
        width: 55px;
        height: 55px;
    }
    
    .login-header {
        margin-bottom: 20px;
    }
    
    .input-group {
        margin-bottom: 20px;
    }
    
    .login-header h1 {
        font-size: 28px;
        margin-bottom: 5px;
    }
    
    .login-subtitle {
        margin-bottom: 10px;
    }
    
    .form-options {
        margin-bottom: 20px;
    }
    
    .features-highlights {
        padding-top: 15px;
        margin-top: 20px;
    }
    
    .copyright {
        bottom: 10px;
    }
}

@media (orientation: landscape) and (max-height: 600px) {
    .login-page {
        height: auto;
        min-height: 100vh;
        padding: 30px 0;
    }
    
    .login-container {
        margin: 30px auto;
    }
    
    .features-highlights {
        display: none;
    }
    
    .copyright {
        position: relative;
        margin-top: 20px;
    }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    :root {
        --body-bg: #1a1f36;
        --dark-color: #f9fbfd;
        --light-color: #2d3748;
        --border-color: #2d3748;
        --secondary-color: #a0aec0;
        --glassmorphism-bg: rgba(26, 32, 55, 0.8);
    }
    
    .particles-container {
        background-image: linear-gradient(135deg, #111827 0%, #1a1f36 100%);
    }
    
    .input-group input {
        background-color: rgba(26, 32, 55, 0.7);
        color: #e2e8f0;
    }
    
    .input-group input::placeholder {
        color: #718096;
    }
    
    .login-subtitle {
        color: #a0aec0;
    }
    
    .custom-checkbox::before {
        background-color: rgba(26, 32, 55, 0.7);
    }
    
    .copyright {
        color: #a0aec0;
    }
    
    .social-icon {
        background-color: #2d3748;
        color: #cbd5e0;
    }
}

/* 增强视觉元素和过渡效果 */
.login-form form {
    position: relative;
    z-index: 2;
}

/* 输入框聚焦时的波纹效果 */
.input-group::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(84, 153, 255, 0.4) 0%, rgba(84, 153, 255, 0) 80%);
    opacity: 0;
    z-index: 0;
    pointer-events: none;
    transition: opacity 0.6s;
    border-radius: 14px;
    transform: scale(0.95);
}

.input-group:focus-within::after {
    opacity: 1;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(0.95);
    }
}

/* 增强社交登录按钮视觉效果 */
.social-login {
    text-align: center;
    padding-top: 25px;
    border-top: 1px solid var(--border-color);
    margin-top: 10px;
    position: relative;
    margin-bottom: 10px;
}

.social-login::before {
    content: '或';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--glassmorphism-bg);
    padding: 0 15px;
    color: var(--secondary-color);
    font-size: 14px;
    border-radius: 10px;
}

.social-login p {
    font-size: 15px;
    color: var(--secondary-color);
    margin-bottom: 22px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 24px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 55px;
    height: 55px;
    border-radius: 50%;
    background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
    color: var(--secondary-color);
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 6px 15px rgba(0,0,0,0.15);
    border: var(--glassmorphism-border);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.social-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 0;
}

.social-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: inset 0 0 0 2px var(--primary-color);
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.social-icon span,
.social-icon i {
    position: relative;
    z-index: 1;
    font-size: 24px;
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-10px) scale(1.15);
    box-shadow: 0 15px 25px rgba(44, 123, 229, 0.3);
    color: white;
}

.social-icon:hover::before {
    opacity: 1;
}

.social-icon:hover::after {
    opacity: 0.5;
    transform: scale(1.1);
    box-shadow: inset 0 0 0 3px rgba(255, 255, 255, 0.6);
}

.social-icon:active {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 15px rgba(44, 123, 229, 0.25);
}

/* 现代化社交图标样式 */
.icon-wechat {
    color: #07C160;
    font-size: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.icon-qq {
    color: #1296db;
    font-size: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

.icon-email {
    color: #ea4335;
    font-size: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

/* 现代化悬停效果 */
.social-icon:hover .icon-wechat,
.social-icon:hover .icon-qq,
.social-icon:hover .icon-email {
    color: white;
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3));
}

/* 社交按钮动画序列 */
.social-icons {
    perspective: 1000px;
}

.social-icons a:nth-child(1) {
    animation: socialFadeIn 0.6s 0.4s backwards;
}

.social-icons a:nth-child(2) {
    animation: socialFadeIn 0.6s 0.6s backwards;
}

.social-icons a:nth-child(3) {
    animation: socialFadeIn 0.6s 0.8s backwards;
}

@keyframes socialFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 社交按钮悬停光晕效果 */
.social-icon .hover-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    mix-blend-mode: overlay;
    pointer-events: none;
    z-index: 0;
    transform: translateZ(0);
}

.social-icon:hover .hover-glow {
    opacity: 0.5;
    animation: glowPulse 2s infinite;
}

@keyframes glowPulse {
    0% { transform: scale(0.95); opacity: 0; }
    50% { transform: scale(1.05); opacity: 0.7; }
    100% { transform: scale(0.95); opacity: 0; }
}

/* 社交按钮图标脉冲效果 */
.social-icon:hover span,
.social-icon:hover i {
    animation: iconPulse 0.8s infinite alternate;
}

@keyframes iconPulse {
    from { transform: scale(1); }
    to { transform: scale(1.2); }
}

/* 社交登录响应式调整 */
@media (max-width: 576px) {
    .social-icons {
        gap: 18px;
    }
    
    .social-icon {
        width: 48px;
        height: 48px;
    }
    
    .social-icon span,
    .social-icon i {
        font-size: 20px;
    }
}

/* 增强功能亮点视觉效果 */
.features-highlights {
    display: flex;
    justify-content: space-around;
    margin-top: 35px;
    border-top: 1px solid var(--border-color);
    padding-top: 30px;
    position: relative;
}

.features-highlights::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 55px;
    height: 55px;
    border-radius: 14px;
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 8px 20px rgba(44, 123, 229, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: iconShine 3s infinite ease-in-out;
}

@keyframes iconShine {
    0% { transform: translateX(-150%) rotate(45deg); }
    50%, 100% { transform: translateX(150%) rotate(45deg); }
}

.feature:hover {
    transform: translateY(-8px);
}

.feature:hover .feature-icon {
    transform: scale(1.15);
    box-shadow: 0 12px 25px rgba(44, 123, 229, 0.4);
}

.feature-text {
    font-size: 15px;
    font-weight: 600;
    color: var(--dark-color);
    transition: color 0.3s ease;
}

.feature:hover .feature-text {
    color: var(--primary-color);
}

/* 添加元素淡入动画序列 */
@keyframes fadeInSequence {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    animation: fadeInSequence 0.8s ease-out both;
}

.login-form {
    opacity: 0;
    animation: fadeInSequence 0.8s ease-out 0.3s forwards;
}

.features-highlights {
    opacity: 0;
    animation: fadeInSequence 0.8s ease-out 0.6s forwards;
}

.copyright {
    opacity: 0;
    animation: fadeInSequence 0.8s ease-out 0.9s forwards;
}

/* 悬停状态增强 */
.login-btn {
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
    z-index: -1;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), #7c4dff);
    z-index: -2;
    transition: opacity 0.3s ease;
    opacity: 1;
}

.login-btn:hover::after {
    background: linear-gradient(135deg, #6c63ff, var(--primary-light));
}

/* 输入框验证动画改进 */
@keyframes shake {
    0%, 100% {transform: translateX(0);}
    10%, 30%, 50%, 70%, 90% {transform: translateX(-4px);}
    20%, 40%, 60%, 80% {transform: translateX(4px);}
}

.shake-animation {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* 页面加载动画优化 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-light);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: opacity 0.6s ease, visibility 0.6s ease;
}

.page-loader.loaded {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

/* 高级加载动画 */
.spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top-color: var(--primary-color);
    animation: spin 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
}

.spinner-ring:nth-child(1) {
    animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
    width: 80%;
    height: 80%;
    border-top-color: #7c4dff;
    animation-delay: 0.15s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    width: 60%;
    height: 60%;
    border-top-color: var(--info-color);
    animation-delay: 0.3s;
}

.spinner-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(44, 123, 229, 0.5);
    animation: spinnerDot 1.5s ease-in-out infinite;
}

@keyframes spinnerDot {
    0%, 100% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.page-loader p {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 18px;
    letter-spacing: 1px;
    margin: 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(90deg, var(--primary-color), #7c4dff, var(--primary-color));
    background-size: 200% auto;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    animation: textGradient 2s linear infinite;
}

@keyframes textGradient {
    0% {
        background-position: 0% center;
    }
    100% {
        background-position: 200% center;
    }
}

/* 加载进度效果 */
.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(44, 123, 229, 0.2);
    border-radius: 2px;
    margin-top: 15px;
    position: relative;
    overflow: hidden;
}

.loading-bar {
    position: absolute;
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, var(--primary-color), #7c4dff);
    border-radius: 2px;
    animation: progressBar 2.5s ease-in-out forwards;
}

@keyframes progressBar {
    0% {
        width: 0;
    }
    50% {
        width: 70%;
    }
    100% {
        width: 100%;
    }
}

/* 数据分析主题页面内容动画序列 */
.login-container {
    animation: dataAnalysisEntrance 1.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}

@keyframes dataAnalysisEntrance {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
        filter: blur(10px);
    }
    30% {
        opacity: 0.3;
        transform: translateY(20px) scale(0.95);
        filter: blur(5px);
    }
    60% {
        opacity: 0.7;
        transform: translateY(-5px) scale(1.02);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* 数据可视化元素序列动画 */
.math-formula {
    animation-delay: calc(var(--animation-order, 0) * 0.1s);
    animation: dataElementFloat 1s ease-out both;
}

@keyframes dataElementFloat {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 全局过渡效果 */
*, *::before, *::after {
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* 修改index.html的page-loader部分 */
/* 
在index.html中替换页面加载动画部分为：

<div class="page-loader">
    <div class="loader-content">
        <div class="spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-dot"></div>
        </div>
        <p>正在加载...</p>
        <div class="loading-progress">
            <div class="loading-bar"></div>
        </div>
    </div>
</div>
*/
