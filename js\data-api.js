/**
 * 数据管理相关API接口
 */

/**
 * 数据管理API服务类
 */
class DataAPI {
    constructor(httpClient) {
        this.client = httpClient;
    }

    /**
     * 上传数据文件
     * @param {File} file - 要上传的文件
     * @param {Object} options - 上传选项
     * @returns {Promise<Object>} 上传结果
     */
    async uploadFile(file, options = {}) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            // 添加额外的参数
            if (options.description) {
                formData.append('description', options.description);
            }
            if (options.tags) {
                formData.append('tags', JSON.stringify(options.tags));
            }

            const response = await this.client.upload('/data/upload', formData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '文件上传成功',
                    data: {
                        fileId: response.data.fileId,
                        fileName: response.data.fileName,
                        fileSize: response.data.fileSize,
                        uploadTime: response.data.uploadTime,
                        parseResult: response.data.parseResult
                    }
                };
            }

            throw new APIError('文件上传失败', 400);
        } catch (error) {
            console.error('文件上传失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('文件上传失败，请检查网络连接', 0);
        }
    }

    /**
     * 解析数据文件
     * @param {string} fileId - 文件ID
     * @param {Object} parseOptions - 解析选项
     * @returns {Promise<Object>} 解析结果
     */
    async parseFile(fileId, parseOptions = {}) {
        try {
            const response = await this.client.post('/data/parse', {
                fileId: fileId,
                options: parseOptions
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '数据解析成功',
                    data: response.data
                };
            }

            throw new APIError('数据解析失败', 400);
        } catch (error) {
            console.error('数据解析失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('数据解析失败', 0);
        }
    }

    /**
     * 获取历史数据列表
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 历史数据列表
     */
    async getHistoryData(params = {}) {
        try {
            const queryParams = {
                page: params.page || 1,
                limit: params.limit || 20,
                sortBy: params.sortBy || 'uploadTime',
                sortOrder: params.sortOrder || 'desc'
            };

            if (params.startDate) {
                queryParams.startDate = params.startDate;
            }
            if (params.endDate) {
                queryParams.endDate = params.endDate;
            }
            if (params.fileType) {
                queryParams.fileType = params.fileType;
            }

            const response = await this.client.get('/data/history', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        items: response.data.items,
                        total: response.data.total,
                        page: response.data.page,
                        limit: response.data.limit,
                        totalPages: response.data.totalPages
                    }
                };
            }

            throw new APIError('获取历史数据失败', 400);
        } catch (error) {
            console.error('获取历史数据失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取历史数据失败', 0);
        }
    }

    /**
     * 获取特定数据文件的详细信息
     * @param {string} fileId - 文件ID
     * @returns {Promise<Object>} 文件详细信息
     */
    async getFileDetails(fileId) {
        try {
            const response = await this.client.get(`/data/files/${fileId}`);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取文件详情失败', 404);
        } catch (error) {
            console.error('获取文件详情失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取文件详情失败', 0);
        }
    }

    /**
     * 删除数据文件
     * @param {string} fileId - 文件ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteFile(fileId) {
        try {
            const response = await this.client.delete(`/data/files/${fileId}`);

            if (response.success) {
                return {
                    success: true,
                    message: '文件删除成功'
                };
            }

            throw new APIError('文件删除失败', 400);
        } catch (error) {
            console.error('文件删除失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('文件删除失败', 0);
        }
    }

    /**
     * 更新文件信息
     * @param {string} fileId - 文件ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateFile(fileId, updateData) {
        try {
            const response = await this.client.put(`/data/files/${fileId}`, updateData);

            if (response.success && response.data) {
                return {
                    success: true,
                    message: '文件信息更新成功',
                    data: response.data
                };
            }

            throw new APIError('文件信息更新失败', 400);
        } catch (error) {
            console.error('文件信息更新失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('文件信息更新失败', 0);
        }
    }

    /**
     * 获取数据统计信息
     * @param {Object} params - 统计参数
     * @returns {Promise<Object>} 统计信息
     */
    async getDataStatistics(params = {}) {
        try {
            const response = await this.client.get('/data/statistics', params);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取数据统计失败', 400);
        } catch (error) {
            console.error('获取数据统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取数据统计失败', 0);
        }
    }

    /**
     * 批量操作数据文件
     * @param {string} action - 操作类型 (delete, export, etc.)
     * @param {Array} fileIds - 文件ID列表
     * @param {Object} options - 操作选项
     * @returns {Promise<Object>} 操作结果
     */
    async batchOperation(action, fileIds, options = {}) {
        try {
            const response = await this.client.post('/data/batch', {
                action: action,
                fileIds: fileIds,
                options: options
            });

            if (response.success) {
                return {
                    success: true,
                    message: `批量${action}操作成功`,
                    data: response.data
                };
            }

            throw new APIError(`批量${action}操作失败`, 400);
        } catch (error) {
            console.error(`批量${action}操作失败:`, error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError(`批量${action}操作失败`, 0);
        }
    }

    /**
     * 验证文件格式
     * @param {File} file - 要验证的文件
     * @returns {Object} 验证结果
     */
    validateFileFormat(file) {
        const supportedTypes = [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/json',
            'text/plain'
        ];

        const supportedExtensions = ['.csv', '.xls', '.xlsx', '.json', '.txt'];
        
        const fileName = file.name.toLowerCase();
        const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
        
        const isValidType = supportedTypes.includes(file.type);
        const isValidExtension = supportedExtensions.includes(fileExtension);
        
        return {
            isValid: isValidType || isValidExtension,
            type: file.type,
            extension: fileExtension,
            size: file.size,
            name: file.name
        };
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 创建数据API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.DataAPI = new DataAPI(window.APIService.client);
}
