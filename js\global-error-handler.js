/**
 * 全局错误处理和用户体验增强模块
 */

/**
 * 全局错误处理器类
 */
class GlobalErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.isProcessing = false;
        this.maxRetries = 3;
        this.retryDelay = 1000;
        this.errorCounts = new Map();
        
        this.init();
    }

    /**
     * 初始化错误处理器
     */
    init() {
        // 捕获未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'unhandledrejection');
            event.preventDefault();
        });

        // 捕获JavaScript运行时错误
        window.addEventListener('error', (event) => {
            this.handleError(event.error || event.message, 'javascript');
        });

        // 捕获资源加载错误
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleResourceError(event);
            }
        }, true);

        // 网络状态监听
        window.addEventListener('online', () => {
            this.handleNetworkStatusChange(true);
        });

        window.addEventListener('offline', () => {
            this.handleNetworkStatusChange(false);
        });
    }

    /**
     * 处理错误
     * @param {Error|string} error - 错误对象或消息
     * @param {string} type - 错误类型
     * @param {Object} context - 错误上下文
     */
    handleError(error, type = 'unknown', context = {}) {
        const errorInfo = {
            message: error?.message || error || '未知错误',
            type: type,
            stack: error?.stack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            context: context
        };

        // 记录错误
        console.error('全局错误捕获:', errorInfo);

        // 统计错误频率
        const errorKey = `${type}:${errorInfo.message}`;
        const count = this.errorCounts.get(errorKey) || 0;
        this.errorCounts.set(errorKey, count + 1);

        // 如果错误频率过高，避免重复处理
        if (count > 5) {
            console.warn('错误频率过高，跳过处理:', errorKey);
            return;
        }

        // 添加到错误队列
        this.errorQueue.push(errorInfo);

        // 处理错误队列
        this.processErrorQueue();

        // 发送错误统计
        this.reportError(errorInfo);
    }

    /**
     * 处理资源加载错误
     * @param {Event} event - 错误事件
     */
    handleResourceError(event) {
        const target = event.target;
        const resourceType = target.tagName.toLowerCase();
        const resourceUrl = target.src || target.href;

        this.handleError(
            new Error(`资源加载失败: ${resourceUrl}`),
            'resource',
            {
                resourceType: resourceType,
                resourceUrl: resourceUrl
            }
        );

        // 尝试重新加载关键资源
        if (resourceType === 'script' && resourceUrl.includes('api-service')) {
            this.retryResourceLoad(target);
        }
    }

    /**
     * 重试资源加载
     * @param {Element} element - 资源元素
     */
    async retryResourceLoad(element) {
        const maxRetries = 3;
        let retries = 0;

        while (retries < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * (retries + 1)));
            
            try {
                const newElement = element.cloneNode(true);
                element.parentNode.replaceChild(newElement, element);
                console.log('资源重新加载成功:', element.src || element.href);
                break;
            } catch (error) {
                retries++;
                console.warn(`资源重新加载失败 (${retries}/${maxRetries}):`, error);
            }
        }
    }

    /**
     * 处理网络状态变化
     * @param {boolean} isOnline - 是否在线
     */
    handleNetworkStatusChange(isOnline) {
        if (isOnline) {
            this.showNotification('网络连接已恢复', 'success');
            this.retryFailedRequests();
        } else {
            this.showNotification('网络连接已断开，请检查网络设置', 'warning', 0);
        }
    }

    /**
     * 重试失败的请求
     */
    async retryFailedRequests() {
        // 重新连接WebSocket
        if (window.wsClient && !window.wsClient.getConnectionStatus()) {
            const token = localStorage.getItem('authToken');
            if (token) {
                try {
                    await window.wsClient.connect(token);
                    console.log('WebSocket重新连接成功');
                } catch (error) {
                    console.warn('WebSocket重新连接失败:', error);
                }
            }
        }

        // 重新验证认证状态
        if (window.AuthGuard) {
            try {
                await window.AuthGuard.checkAuth();
            } catch (error) {
                console.warn('认证状态检查失败:', error);
            }
        }
    }

    /**
     * 处理错误队列
     */
    async processErrorQueue() {
        if (this.isProcessing || this.errorQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        while (this.errorQueue.length > 0) {
            const errorInfo = this.errorQueue.shift();
            await this.processError(errorInfo);
        }

        this.isProcessing = false;
    }

    /**
     * 处理单个错误
     * @param {Object} errorInfo - 错误信息
     */
    async processError(errorInfo) {
        try {
            // 根据错误类型进行不同处理
            switch (errorInfo.type) {
                case 'network':
                    await this.handleNetworkError(errorInfo);
                    break;
                case 'authentication':
                    await this.handleAuthError(errorInfo);
                    break;
                case 'validation':
                    this.handleValidationError(errorInfo);
                    break;
                case 'api':
                    await this.handleApiError(errorInfo);
                    break;
                default:
                    this.handleGenericError(errorInfo);
            }
        } catch (error) {
            console.error('错误处理失败:', error);
        }
    }

    /**
     * 处理网络错误
     * @param {Object} errorInfo - 错误信息
     */
    async handleNetworkError(errorInfo) {
        this.showNotification('网络连接异常，请检查网络设置', 'error');
        
        // 等待网络恢复后重试
        if (!navigator.onLine) {
            return;
        }

        // 重试机制
        await this.retryWithBackoff(async () => {
            // 这里可以添加重试逻辑
            console.log('重试网络请求');
        });
    }

    /**
     * 处理认证错误
     * @param {Object} errorInfo - 错误信息
     */
    async handleAuthError(errorInfo) {
        this.showNotification('登录已过期，请重新登录', 'warning');
        
        // 清除认证信息
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        
        // 延迟跳转到登录页面
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
    }

    /**
     * 处理验证错误
     * @param {Object} errorInfo - 错误信息
     */
    handleValidationError(errorInfo) {
        this.showNotification(errorInfo.message || '输入数据格式错误', 'warning');
    }

    /**
     * 处理API错误
     * @param {Object} errorInfo - 错误信息
     */
    async handleApiError(errorInfo) {
        const message = this.getApiErrorMessage(errorInfo);
        this.showNotification(message, 'error');
    }

    /**
     * 处理通用错误
     * @param {Object} errorInfo - 错误信息
     */
    handleGenericError(errorInfo) {
        const message = this.getGenericErrorMessage(errorInfo);
        this.showNotification(message, 'error');
    }

    /**
     * 获取API错误消息
     * @param {Object} errorInfo - 错误信息
     * @returns {string} 错误消息
     */
    getApiErrorMessage(errorInfo) {
        const message = errorInfo.message || '';
        
        if (message.includes('timeout')) {
            return '请求超时，请稍后重试';
        } else if (message.includes('network')) {
            return '网络连接异常，请检查网络设置';
        } else if (message.includes('unauthorized')) {
            return '认证失败，请重新登录';
        } else if (message.includes('forbidden')) {
            return '权限不足，无法执行此操作';
        } else if (message.includes('not found')) {
            return '请求的资源不存在';
        } else if (message.includes('server error')) {
            return '服务器内部错误，请稍后重试';
        } else {
            return '操作失败，请稍后重试';
        }
    }

    /**
     * 获取通用错误消息
     * @param {Object} errorInfo - 错误信息
     * @returns {string} 错误消息
     */
    getGenericErrorMessage(errorInfo) {
        const message = errorInfo.message || '';
        
        if (message.includes('Script error')) {
            return '脚本加载失败，请刷新页面重试';
        } else if (message.includes('ChunkLoadError')) {
            return '资源加载失败，请刷新页面重试';
        } else {
            return '发生未知错误，请刷新页面重试';
        }
    }

    /**
     * 带退避的重试机制
     * @param {Function} fn - 重试函数
     * @param {number} maxRetries - 最大重试次数
     * @returns {Promise} 重试结果
     */
    async retryWithBackoff(fn, maxRetries = this.maxRetries) {
        let retries = 0;
        
        while (retries < maxRetries) {
            try {
                return await fn();
            } catch (error) {
                retries++;
                if (retries >= maxRetries) {
                    throw error;
                }
                
                const delay = this.retryDelay * Math.pow(2, retries - 1);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     * @param {number} duration - 显示时长（毫秒）
     */
    showNotification(message, type = 'info', duration = 5000) {
        // 优先使用全局toast系统
        if (window.toastSystem) {
            window.toastSystem.show(type, '', message, duration);
        } else if (window.showToast) {
            window.showToast(type, '', message, duration);
        } else if (window.showMessage) {
            window.showMessage(message, type, duration);
        } else {
            // 回退到console和alert
            console.log(`[${type.toUpperCase()}] ${message}`);
            if (type === 'error') {
                alert(message);
            }
        }
    }

    /**
     * 报告错误到后端
     * @param {Object} errorInfo - 错误信息
     */
    async reportError(errorInfo) {
        try {
            // 静默记录错误统计，不影响主要功能
            if (window.statsAPI && window.statsAPI.isStatsEnabled()) {
                window.statsAPI.trackUserAction({
                    action: 'error_occurred',
                    feature: 'error_tracking',
                    metadata: {
                        errorType: errorInfo.type,
                        errorMessage: errorInfo.message,
                        errorStack: errorInfo.stack,
                        errorContext: errorInfo.context
                    }
                }).catch(error => {
                    // 静默处理统计错误
                    console.warn('错误统计失败:', error);
                });
            }
        } catch (error) {
            // 静默处理，不影响主要功能
            console.warn('错误报告失败:', error);
        }
    }

    /**
     * 清理错误统计
     */
    clearErrorStats() {
        this.errorCounts.clear();
    }
}

// 创建全局错误处理器实例
if (typeof window !== 'undefined') {
    window.GlobalErrorHandler = GlobalErrorHandler;
    window.globalErrorHandler = new GlobalErrorHandler();
}
