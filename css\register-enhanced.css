/* 注册页面增强样式 */

/* 注册页面布局 - 完全居中 */
.login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}

/* 注册容器特殊样式 */
.register-container {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(255, 255, 255, 0.95) 50%,
        rgba(255, 255, 255, 0.98) 100%);
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow:
        0 25px 50px rgba(44, 123, 229, 0.15),
        0 15px 35px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    position: relative;
    overflow: hidden;
    width: 400px;
    max-width: 90vw;
    padding: 30px 35px;
    margin: 0;
    transform: none;
    top: auto;
    left: auto;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    z-index: 10;
}

/* 注册容器悬停效果 - 与登录页面一致 */
.register-container:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(44, 123, 229, 0.25),
        0 20px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(44, 123, 229, 0.3);
}

/* 注册容器渐变边框装饰 */
.register-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(44, 123, 229, 0.4),
        rgba(0, 217, 255, 0.4),
        rgba(255, 123, 0, 0.4),
        rgba(255, 0, 144, 0.4));
    background-size: 400% 400%;
    border-radius: 30px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.6s ease;
    animation: gradientShift 8s ease infinite;
}

.register-container:hover::before {
    opacity: 0.7;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 数据图表装饰线条 */
.register-container::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><path d="M10 50 L20 30 L30 40 L40 20 L50 35" stroke="rgba(44,123,229,0.2)" stroke-width="2" fill="none"/><circle cx="20" cy="30" r="2" fill="rgba(44,123,229,0.3)"/><circle cx="30" cy="40" r="2" fill="rgba(44,123,229,0.3)"/><circle cx="40" cy="20" r="2" fill="rgba(44,123,229,0.3)"/><circle cx="50" cy="35" r="2" fill="rgba(44,123,229,0.3)"/></svg>') no-repeat center;
    opacity: 0.1;
    transition: opacity 0.3s ease;
}

.register-container:hover::after {
    opacity: 0.3;
}

.register-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(44, 123, 229, 0.03),
        transparent,
        rgba(139, 92, 246, 0.03),
        transparent
    );
    animation: rotate 20s linear infinite;
    pointer-events: none;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.register-container .login-content {
    position: relative;
    z-index: 1;
}

/* 注册图标样式 */
.register-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 25px auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.3),
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.register-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: iconRotate 8s linear infinite;
}

.register-icon i {
    width: 36px;
    height: 36px;
    color: white;
    z-index: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
}

@keyframes iconRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 注册图标悬停效果 */
.register-container:hover .register-icon {
    transform: translateY(-8px) scale(1.1) rotate(5deg);
    filter: drop-shadow(0 12px 30px rgba(102, 126, 234, 0.5));
    box-shadow:
        0 20px 40px rgba(102, 126, 234, 0.4),
        0 12px 25px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

.register-container:hover .register-icon i {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* 标题美化 */
.login-header {
    text-align: center;
    margin-bottom: 35px;
    padding-top: 15px;
}

.login-header h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 12px;
    text-align: center;
    position: relative;
}

.login-header h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.login-subtitle {
    color: var(--secondary-color);
    font-size: 15px;
    text-align: center;
    margin-bottom: 20px;
    font-weight: 400;
    line-height: 1.5;
}

/* 手写艺术字样式 */
.handwriting-container {
    text-align: center;
    margin: 0;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.1);
}

.handwriting-container.show {
    opacity: 1;
    transform: translateY(0);
}

.handwriting-svg {
    width: 350px;
    height: 32px;
    filter: drop-shadow(0 2px 8px rgba(102, 126, 234, 0.25));
    transform: scale(1);
    transition: transform 0.3s ease;
}

.handwriting-container:hover .handwriting-svg {
    transform: scale(1.05);
}

.handwriting-svg path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    opacity: 0;
}

/* 手写动画效果 */
.handwriting-container.animate path {
    animation: handwriteReveal 0.8s ease-out forwards;
    opacity: 1;
}

/* 为每个字母设置不同的延迟 - 正确字母版本 */
.handwriting-container.animate #letter-w { animation-delay: 0s; animation-duration: 0.8s; }
.handwriting-container.animate #letter-e1 { animation-delay: 0.15s; animation-duration: 1s; }
.handwriting-container.animate #letter-a1 { animation-delay: 0.4s; animation-duration: 0.8s; }
.handwriting-container.animate #letter-r1 { animation-delay: 0.55s; animation-duration: 0.9s; }
.handwriting-container.animate #letter-e2 { animation-delay: 0.7s; animation-duration: 1s; }
.handwriting-container.animate #letter-o1 { animation-delay: 0.95s; animation-duration: 1.2s; }
.handwriting-container.animate #letter-n1 { animation-delay: 1.15s; animation-duration: 0.8s; }
.handwriting-container.animate #letter-t1 { animation-delay: 1.4s; animation-duration: 0.7s; }
.handwriting-container.animate #letter-h1 { animation-delay: 1.55s; animation-duration: 0.9s; }
.handwriting-container.animate #letter-e3 { animation-delay: 1.75s; animation-duration: 1s; }
.handwriting-container.animate #letter-w2 { animation-delay: 2.0s; animation-duration: 0.8s; }
.handwriting-container.animate #letter-a2 { animation-delay: 2.15s; animation-duration: 0.8s; }
.handwriting-container.animate #letter-y1 { animation-delay: 2.3s; animation-duration: 1s; }
.handwriting-container.animate #arrow { animation-delay: 2.6s; animation-duration: 1s; }
.handwriting-container.animate #heart { animation-delay: 2.8s; animation-duration: 1.2s; }

@keyframes handwriteReveal {
    0% {
        stroke-dashoffset: 1000;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    100% {
        stroke-dashoffset: 0;
        opacity: 1;
    }
}

/* 完成后的闪烁效果 */
.handwriting-container.animate.complete {
    animation: handwriteGlow 2s ease-in-out infinite;
}

@keyframes handwriteGlow {
    0%, 100% {
        filter: drop-shadow(0 2px 8px rgba(102, 126, 234, 0.2));
    }
    50% {
        filter: drop-shadow(0 4px 16px rgba(102, 126, 234, 0.4));
    }
}

/* 欢迎通知样式 */
.welcome-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    animation: slideInDown 0.6s ease-out;
    max-width: 400px;
    width: 90%;
}

.welcome-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    color: white;
    position: relative;
}

.welcome-content i {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    flex-shrink: 0;
}

.welcome-text h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
}

.welcome-text p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.welcome-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.welcome-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.welcome-close i {
    width: 16px;
    height: 16px;
}

/* 验证码样式 */
.captcha-group {
    position: relative;
}

.captcha-container {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 6px;
}

#captcha-canvas {
    border: 2px solid #e3ebf6;
    border-radius: 6px;
    background: #f8fafc;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 90px;
    height: 36px;
}

#captcha-canvas:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(44, 123, 229, 0.1);
}

.captcha-refresh {
    background: var(--primary-color);
    border: none;
    border-radius: 6px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
}

.captcha-refresh:hover {
    background: var(--primary-dark);
    transform: rotate(180deg);
}

.captcha-refresh i {
    width: 16px;
    height: 16px;
}

.input-group.captcha-group input {
    padding-right: 150px;
}

/* 输入框图标样式 */
.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: var(--secondary-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    z-index: 2;
}

.input-group:focus-within .input-icon {
    color: var(--primary-color);
    transform: translateY(-50%) scale(1.15) rotate(5deg);
    filter: drop-shadow(0 2px 4px rgba(44, 123, 229, 0.3));
}

.input-group input {
    padding-left: 50px;
    transition: all 0.3s ease;
}

.input-group.captcha-group input {
    padding-right: 140px;
}

/* 输入框增强动效 */
.input-group {
    position: relative;
    margin-bottom: 20px;
    overflow: visible;
}

/* 优化输入框布局 */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.login-form .input-group:last-of-type {
    margin-bottom: 18px;
}

.input-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    background: linear-gradient(45deg, transparent, rgba(44, 123, 229, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.input-group:focus-within::before {
    opacity: 1;
}

.input-group input:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 123, 229, 0.15);
}

/* 按钮增强动效 */
.login-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(44, 123, 229, 0.3);
}

.login-btn:active {
    transform: translateY(-1px) scale(0.98);
}

/* 表单容器动效 */
.login-form {
    animation: formSlideIn 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.1) 0.3s both;
}

@keyframes formSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 复选框美化 */
.form-options {
    margin: 20px 0 24px 0;
}

.agreement {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    line-height: 1.5;
}

.agreement input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.agreement input[type="checkbox"]:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.agreement input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 4px;
    height: 8px;
    border: 2px solid white;
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
}

.agreement label {
    font-size: 14px;
    color: var(--secondary-color);
    cursor: pointer;
    user-select: none;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.terms-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* 返回登录链接美化 */
.register-link {
    text-align: center;
    margin-top: 25px;
    font-size: 14px;
    color: var(--secondary-color);
}

.register-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.register-link a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.register-link a:hover::after {
    width: 100%;
}

.register-link a:hover {
    color: var(--primary-dark);
    transform: translateY(-1px);
}

/* 加载状态美化 */
.btn-loading {
    pointer-events: none;
    position: relative;
    color: transparent !important;
}

.btn-loading .btn-text {
    opacity: 0;
}

.btn-loading .btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 4px;
}

.loader-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: white;
    animation: loadingDots 1.4s infinite ease-in-out;
}

.loader-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loader-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 页面加载器相关样式已完全移除 */

/* 密码切换按钮样式 */
.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--secondary-color);
    transition: all 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(44, 123, 229, 0.1);
}

.password-toggle i {
    width: 18px;
    height: 18px;
}

/* 关闭按钮样式 */
.close-modal {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--secondary-color);
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: var(--danger-color);
    background: rgba(230, 55, 87, 0.1);
}

.close-modal i {
    width: 20px;
    height: 20px;
}

/* 输入验证提示样式 */
.input-validation {
    position: absolute;
    bottom: -25px;
    left: 0;
    right: 0;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.input-validation.show {
    opacity: 1;
    transform: translateY(0);
}

.input-validation.success {
    color: var(--success-color);
    background: rgba(0, 217, 126, 0.1);
}

.input-validation.error {
    color: var(--danger-color);
    background: rgba(230, 55, 87, 0.1);
}

.input-validation.warning {
    color: var(--warning-color);
    background: rgba(246, 195, 67, 0.1);
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.password-strength-bar {
    flex: 1;
    height: 4px;
    background: #e3ebf6;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-weak .password-strength-fill {
    width: 33%;
    background: var(--danger-color);
}

.password-strength-medium .password-strength-fill {
    width: 66%;
    background: var(--warning-color);
}

.password-strength-strong .password-strength-fill {
    width: 100%;
    background: var(--success-color);
}

/* 密码匹配指示器 */
.password-match {
    margin-top: 8px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.password-match i {
    width: 14px;
    height: 14px;
}

/* 动画效果 */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(-30px);
    }
}

/* 增强通知样式 */
.enhanced-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    max-width: 400px;
    width: calc(100% - 40px);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    overflow: hidden;
}

.enhanced-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.enhanced-notification-content {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    gap: 15px;
}

.enhanced-notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.enhanced-notification-success .enhanced-notification-icon {
    background: rgba(0, 217, 126, 0.15);
    color: var(--success-color);
}

.enhanced-notification-error .enhanced-notification-icon {
    background: rgba(230, 55, 87, 0.15);
    color: var(--danger-color);
}

.enhanced-notification-warning .enhanced-notification-icon {
    background: rgba(246, 195, 67, 0.15);
    color: var(--warning-color);
}

.enhanced-notification-info .enhanced-notification-icon {
    background: rgba(44, 123, 229, 0.15);
    color: var(--primary-color);
}

.enhanced-notification-icon i {
    width: 20px;
    height: 20px;
}

.enhanced-notification-text {
    flex: 1;
}

.enhanced-notification-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.enhanced-notification-message {
    font-size: 14px;
    color: var(--secondary-color);
    line-height: 1.4;
}

.enhanced-notification-close {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--secondary-color);
    transition: all 0.2s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
}

.enhanced-notification-close:hover {
    color: var(--danger-color);
    background: rgba(230, 55, 87, 0.1);
}

.enhanced-notification-close i {
    width: 16px;
    height: 16px;
}

.enhanced-notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--success-color);
    width: 100%;
    animation: progressBar 3s linear;
}

@keyframes progressBar {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* Logo美化 */
.logo {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    filter: drop-shadow(0 4px 15px rgba(44, 123, 229, 0.2));
}

.logo:hover {
    transform: scale(1.05) rotate(2deg);
    filter: drop-shadow(0 8px 25px rgba(44, 123, 229, 0.3));
}

/* 版权信息美化 */
.copyright {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    z-index: 1;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 输入框聚焦时的标签动画 */
.input-group {
    position: relative;
}

.input-group input:focus + .input-focus-border {
    transform: scaleX(1);
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform 0.3s ease;
    border-radius: 1px;
}

/* 模态窗口美化 */
.modal {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.6);
}

.modal-content {
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-header h2 {
    color: var(--dark-color);
    font-size: 20px;
    font-weight: 600;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-page {
        padding: 15px !important;
        align-items: flex-start !important;
        padding-top: 50px !important;
    }

    .register-container {
        width: 100% !important;
        max-width: 400px !important;
        padding: 30px 25px !important;
        margin: 0 !important;
    }

    .captcha-container {
        position: static;
        transform: none;
        margin-top: 10px;
        justify-content: center;
    }

    .input-group.captcha-group input {
        padding-right: 16px;
    }

    .input-group.captcha-group {
        flex-direction: column;
        align-items: stretch;
    }

    .login-header h1 {
        font-size: 28px;
    }

    .login-subtitle {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .login-page {
        padding: 10px !important;
        padding-top: 30px !important;
    }

    .register-container {
        width: 100% !important;
        max-width: 350px !important;
        padding: 25px 20px !important;
    }

    .input-group {
        margin-bottom: 18px;
    }

    .login-header h1 {
        font-size: 24px;
    }
}

@media (max-height: 700px) {
    .login-page {
        align-items: flex-start !important;
        padding-top: 20px !important;
    }

    .register-container {
        padding: 25px !important;
    }

    .login-header {
        margin-bottom: 20px;
    }

    .input-group {
        margin-bottom: 16px;
    }
}

@media (max-width: 480px) {
    .register-container {
        margin: 15px;
        padding: 25px 20px;
    }

    .input-group {
        margin-bottom: 20px;
    }

    .login-header h1 {
        font-size: 24px;
    }

    .welcome-notification {
        top: 5px;
        max-width: 98%;
    }

    .welcome-content {
        padding: 12px 16px;
    }

    .welcome-text h3 {
        font-size: 15px;
    }

    .welcome-text p {
        font-size: 13px;
    }
}

/* 高度较小的屏幕优化 */
@media (max-height: 700px) {
    .register-container {
        margin: 10px auto;
        padding: 25px;
    }

    .login-header {
        margin-bottom: 25px;
    }

    .input-group {
        margin-bottom: 18px;
    }

    .welcome-notification {
        top: 5px;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .register-container {
        background: linear-gradient(145deg,
            rgba(30, 41, 59, 0.98) 0%,
            rgba(30, 41, 59, 0.95) 50%,
            rgba(30, 41, 59, 0.98) 100%);
        border-color: rgba(255, 255, 255, 0.1);
    }

    .login-header h1 {
        background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .input-group input {
        background: rgba(255, 255, 255, 0.05);
        border-color: rgba(255, 255, 255, 0.1);
        color: #f1f5f9;
    }

    .input-group input:focus {
        background: rgba(255, 255, 255, 0.08);
        border-color: var(--primary-color);
    }

    .copyright {
        color: rgba(255, 255, 255, 0.6);
        background: rgba(0, 0, 0, 0.2);
    }
}
