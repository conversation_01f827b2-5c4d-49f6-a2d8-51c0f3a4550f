/**
 * 反馈系统相关API接口
 */

/**
 * 反馈API服务类
 */
class FeedbackAPI {
    constructor(httpClient) {
        this.client = httpClient;
    }

    /**
     * 提交反馈
     * @param {Object} feedback - 反馈信息
     * @returns {Promise<Object>} 提交结果
     */
    async submitFeedback(feedback) {
        try {
            const requestData = {
                type: feedback.type || 'general', // bug, feature, general, improvement
                title: feedback.title,
                description: feedback.description,
                priority: feedback.priority || 'medium', // low, medium, high, urgent
                category: feedback.category || 'other', // ui, performance, feature, bug, other
                attachments: feedback.attachments || [],
                metadata: {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString(),
                    ...feedback.metadata
                }
            };

            const response = await this.client.post('/feedback', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        feedbackId: response.data.feedbackId,
                        ticketNumber: response.data.ticketNumber,
                        status: response.data.status,
                        createdAt: response.data.createdAt
                    }
                };
            }

            throw new APIError('反馈提交失败', 400);
        } catch (error) {
            console.error('反馈提交失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('反馈提交失败', 0);
        }
    }

    /**
     * 获取反馈列表
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 反馈列表
     */
    async getFeedbackList(params = {}) {
        try {
            const queryParams = {
                page: params.page || 1,
                limit: params.limit || 20,
                type: params.type,
                status: params.status, // open, in_progress, resolved, closed
                priority: params.priority,
                category: params.category,
                startDate: params.startDate,
                endDate: params.endDate,
                search: params.search
            };

            const response = await this.client.get('/feedback', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        feedbacks: response.data.feedbacks || [],
                        total: response.data.total || 0,
                        page: response.data.page || 1,
                        limit: response.data.limit || 20,
                        totalPages: response.data.totalPages || 0
                    }
                };
            }

            throw new APIError('获取反馈列表失败', 400);
        } catch (error) {
            console.error('获取反馈列表失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取反馈列表失败', 0);
        }
    }

    /**
     * 获取反馈详情
     * @param {string} feedbackId - 反馈ID
     * @returns {Promise<Object>} 反馈详情
     */
    async getFeedbackDetail(feedbackId) {
        try {
            const response = await this.client.get(`/feedback/${feedbackId}`);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取反馈详情失败', 400);
        } catch (error) {
            console.error('获取反馈详情失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取反馈详情失败', 0);
        }
    }

    /**
     * 添加评论
     * @param {string} feedbackId - 反馈ID
     * @param {Object} comment - 评论信息
     * @returns {Promise<Object>} 添加结果
     */
    async addComment(feedbackId, comment) {
        try {
            const requestData = {
                content: comment.content,
                attachments: comment.attachments || [],
                isInternal: comment.isInternal || false
            };

            const response = await this.client.post(`/feedback/${feedbackId}/comments`, requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        commentId: response.data.commentId,
                        createdAt: response.data.createdAt
                    }
                };
            }

            throw new APIError('添加评论失败', 400);
        } catch (error) {
            console.error('添加评论失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('添加评论失败', 0);
        }
    }

    /**
     * 获取评论列表
     * @param {string} feedbackId - 反馈ID
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 评论列表
     */
    async getComments(feedbackId, params = {}) {
        try {
            const queryParams = {
                page: params.page || 1,
                limit: params.limit || 50,
                includeInternal: params.includeInternal || false
            };

            const response = await this.client.get(`/feedback/${feedbackId}/comments`, queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        comments: response.data.comments || [],
                        total: response.data.total || 0,
                        page: response.data.page || 1,
                        limit: response.data.limit || 50
                    }
                };
            }

            throw new APIError('获取评论列表失败', 400);
        } catch (error) {
            console.error('获取评论列表失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取评论列表失败', 0);
        }
    }

    /**
     * 更新反馈状态
     * @param {string} feedbackId - 反馈ID
     * @param {string} status - 新状态
     * @param {string} reason - 更新原因
     * @returns {Promise<Object>} 更新结果
     */
    async updateFeedbackStatus(feedbackId, status, reason = '') {
        try {
            const requestData = {
                status: status,
                reason: reason
            };

            const response = await this.client.put(`/feedback/${feedbackId}/status`, requestData);

            if (response.success) {
                return {
                    success: true,
                    message: '反馈状态更新成功'
                };
            }

            throw new APIError('更新反馈状态失败', 400);
        } catch (error) {
            console.error('更新反馈状态失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('更新反馈状态失败', 0);
        }
    }

    /**
     * 跟踪反馈进度
     * @param {string} feedbackId - 反馈ID
     * @returns {Promise<Object>} 跟踪结果
     */
    async trackFeedback(feedbackId) {
        try {
            const response = await this.client.get(`/feedback/${feedbackId}/track`);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        feedbackId: response.data.feedbackId,
                        ticketNumber: response.data.ticketNumber,
                        status: response.data.status,
                        priority: response.data.priority,
                        assignee: response.data.assignee,
                        timeline: response.data.timeline || [],
                        estimatedResolution: response.data.estimatedResolution,
                        lastUpdate: response.data.lastUpdate
                    }
                };
            }

            throw new APIError('跟踪反馈失败', 400);
        } catch (error) {
            console.error('跟踪反馈失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('跟踪反馈失败', 0);
        }
    }

    /**
     * 上传附件
     * @param {File} file - 文件对象
     * @param {string} feedbackId - 反馈ID（可选）
     * @returns {Promise<Object>} 上传结果
     */
    async uploadAttachment(file, feedbackId = null) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            if (feedbackId) {
                formData.append('feedbackId', feedbackId);
            }

            const response = await this.client.post('/feedback/attachments', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        attachmentId: response.data.attachmentId,
                        filename: response.data.filename,
                        url: response.data.url,
                        size: response.data.size
                    }
                };
            }

            throw new APIError('附件上传失败', 400);
        } catch (error) {
            console.error('附件上传失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('附件上传失败', 0);
        }
    }

    /**
     * 获取反馈统计
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 统计结果
     */
    async getFeedbackStats(params = {}) {
        try {
            const queryParams = {
                startDate: params.startDate,
                endDate: params.endDate,
                groupBy: params.groupBy || 'status' // status, type, priority, category
            };

            const response = await this.client.get('/feedback/stats', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: response.data
                };
            }

            throw new APIError('获取反馈统计失败', 400);
        } catch (error) {
            console.error('获取反馈统计失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取反馈统计失败', 0);
        }
    }

    /**
     * 验证反馈数据
     * @param {Object} feedback - 反馈数据
     * @returns {Object} 验证结果
     */
    validateFeedback(feedback) {
        const errors = [];

        if (!feedback.title || feedback.title.trim().length === 0) {
            errors.push('反馈标题不能为空');
        }

        if (!feedback.description || feedback.description.trim().length === 0) {
            errors.push('反馈描述不能为空');
        }

        if (feedback.title && feedback.title.length > 200) {
            errors.push('反馈标题不能超过200个字符');
        }

        if (feedback.description && feedback.description.length > 5000) {
            errors.push('反馈描述不能超过5000个字符');
        }

        if (feedback.type && !['bug', 'feature', 'general', 'improvement'].includes(feedback.type)) {
            errors.push('无效的反馈类型');
        }

        if (feedback.priority && !['low', 'medium', 'high', 'urgent'].includes(feedback.priority)) {
            errors.push('无效的优先级');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 格式化反馈状态
     * @param {string} status - 状态值
     * @returns {string} 格式化后的状态
     */
    formatStatus(status) {
        const statusMap = {
            'open': '待处理',
            'in_progress': '处理中',
            'resolved': '已解决',
            'closed': '已关闭',
            'pending': '等待中'
        };
        return statusMap[status] || status;
    }

    /**
     * 格式化反馈类型
     * @param {string} type - 类型值
     * @returns {string} 格式化后的类型
     */
    formatType(type) {
        const typeMap = {
            'bug': '错误报告',
            'feature': '功能建议',
            'general': '一般反馈',
            'improvement': '改进建议'
        };
        return typeMap[type] || type;
    }

    /**
     * 格式化优先级
     * @param {string} priority - 优先级值
     * @returns {string} 格式化后的优先级
     */
    formatPriority(priority) {
        const priorityMap = {
            'low': '低',
            'medium': '中',
            'high': '高',
            'urgent': '紧急'
        };
        return priorityMap[priority] || priority;
    }
}

// 创建反馈API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.FeedbackAPI = new FeedbackAPI(window.APIService.client);
}
