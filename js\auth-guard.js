/**
 * 认证守卫 - 保护需要登录的页面
 */

/**
 * 认证守卫类
 */
class AuthGuard {
    constructor() {
        this.protectedPages = [
            'data.html',
            'signal.html',
            'serial.html',
            'export.html',
            'feedback.html',
            'statistics.html'
        ];
        
        this.publicPages = [
            'index.html',
            'register.html'
        ];
        
        this.init();
    }

    /**
     * 初始化认证守卫
     */
    init() {
        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', () => {
            this.checkAuthentication();
        });

        // 监听存储变化，处理多标签页登出
        window.addEventListener('storage', (e) => {
            if (e.key === 'auth_token' && !e.newValue) {
                // Token被清除，可能是在其他标签页登出
                this.handleLogout();
            }
        });

        // 定期检查Token有效性
        this.startTokenValidationTimer();
    }

    /**
     * 检查当前页面是否需要认证
     */
    isProtectedPage() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        return this.protectedPages.includes(currentPage);
    }

    /**
     * 检查用户认证状态
     */
    async checkAuthentication() {
        if (!this.isProtectedPage()) {
            return; // 公开页面不需要检查
        }

        try {
            // 检查AuthAPI是否可用
            if (!window.AuthAPI) {
                console.error('AuthAPI未加载');
                this.redirectToLogin();
                return;
            }

            // 检查Token是否有效
            if (!window.AuthAPI.isLoggedIn()) {
                console.log('用户未登录或Token已过期');
                this.redirectToLogin();
                return;
            }

            // 尝试获取用户信息验证Token
            try {
                await window.AuthAPI.getCurrentUser();
                console.log('用户认证有效');
                this.updateUserDisplay();

                // 认证成功后，尝试连接WebSocket
                this.initWebSocketConnection();
            } catch (error) {
                console.warn('Token验证失败:', error);

                // 如果是网络错误或API不可用，不强制跳转
                if (error.status === 0 || error.status === 404 || error.status >= 500) {
                    console.warn('API服务不可用，跳过认证检查');
                    this.showOfflineMode();
                    return;
                }

                // 只有在明确的认证错误时才跳转到登录页
                if (error.status === 401 || error.status === 403) {
                    this.redirectToLogin();
                }
            }

        } catch (error) {
            console.error('认证检查失败:', error);
            this.redirectToLogin();
        }
    }

    /**
     * 更新页面上的用户显示信息
     */
    updateUserDisplay() {
        const userInfo = window.AuthAPI?.getLocalUserInfo();
        if (userInfo) {
            // 更新用户名显示
            const userDisplays = document.querySelectorAll('.user-display, .username-display, .current-user');
            userDisplays.forEach(element => {
                element.textContent = userInfo.username;
            });

            // 更新邮箱显示
            const emailDisplays = document.querySelectorAll('.user-email, .email-display');
            emailDisplays.forEach(element => {
                element.textContent = userInfo.email;
            });

            console.log('用户信息已更新:', userInfo.username);
        }
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocketConnection() {
        if (window.wsClient) {
            // 延迟连接，确保认证已完成
            setTimeout(async () => {
                try {
                    const connected = await window.wsClient.connect();
                    if (connected) {
                        console.log('WebSocket连接已建立');
                    }
                } catch (error) {
                    console.warn('WebSocket连接失败:', error);
                }
            }, 500);
        }
    }

    /**
     * 显示离线模式提示
     */
    showOfflineMode() {
        console.log('进入离线模式');
        // 可以在这里添加离线模式的UI提示
        const offlineIndicator = document.createElement('div');
        offlineIndicator.className = 'offline-indicator';
        offlineIndicator.innerHTML = `
            <div class="offline-message">
                <i class="icon-wifi-off"></i>
                <span>离线模式 - 部分功能可能不可用</span>
            </div>
        `;
        offlineIndicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #ff9800;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        // 移除已存在的离线指示器
        const existing = document.querySelector('.offline-indicator');
        if (existing) {
            existing.remove();
        }

        document.body.appendChild(offlineIndicator);

        // 5秒后自动隐藏
        setTimeout(() => {
            if (offlineIndicator.parentNode) {
                offlineIndicator.remove();
            }
        }, 5000);
    }

    /**
     * 跳转到登录页面
     */
    redirectToLogin() {
        const currentPage = window.location.pathname;
        const loginUrl = 'index.html';
        
        // 如果已经在登录页面，不需要跳转
        if (currentPage.includes('index.html') || currentPage === '/') {
            return;
        }

        console.log('跳转到登录页面');
        
        // 显示提示信息
        if (typeof showMessage === 'function') {
            showMessage('请先登录', 'warning');
        } else {
            alert('请先登录');
        }

        // 延迟跳转，让用户看到提示信息
        setTimeout(() => {
            window.location.href = loginUrl;
        }, 1000);
    }

    /**
     * 处理登出
     */
    handleLogout() {
        if (this.isProtectedPage()) {
            console.log('检测到登出，跳转到登录页面');
            this.redirectToLogin();
        }
    }

    /**
     * 启动Token验证定时器
     */
    startTokenValidationTimer() {
        // 每5分钟检查一次Token有效性
        setInterval(() => {
            if (this.isProtectedPage() && window.AuthAPI) {
                if (!window.AuthAPI.isLoggedIn()) {
                    console.log('Token已过期，自动登出');
                    this.handleLogout();
                }
            }
        }, 5 * 60 * 1000); // 5分钟
    }

    /**
     * 手动登出
     */
    async logout() {
        try {
            if (window.AuthAPI) {
                await window.AuthAPI.logout();
            }
            
            // 跳转到登录页面
            window.location.href = 'index.html';
        } catch (error) {
            console.error('登出失败:', error);
            // 即使失败也要跳转
            window.location.href = 'index.html';
        }
    }

    /**
     * 绑定登出按钮事件
     */
    bindLogoutButtons() {
        const logoutButtons = document.querySelectorAll('.logout-btn, #logoutBtn, [data-action="logout"]');
        
        logoutButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                
                if (confirm('确定要退出登录吗？')) {
                    await this.logout();
                }
            });
        });
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        const token = window.AuthAPI?.tokenManager?.getToken();
        if (token) {
            return {
                'Authorization': `Bearer ${token}`
            };
        }
        return {};
    }

    /**
     * 检查是否有权限访问特定功能
     */
    hasPermission(permission) {
        // 这里可以根据用户角色和权限进行检查
        // 目前简单返回是否已登录
        return window.AuthAPI?.isLoggedIn() || false;
    }
}

// 创建全局认证守卫实例
window.AuthGuard = new AuthGuard();

// 页面加载完成后绑定登出按钮
document.addEventListener('DOMContentLoaded', () => {
    if (window.AuthGuard) {
        window.AuthGuard.bindLogoutButtons();
    }
});

// 导出认证守卫
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthGuard;
}
