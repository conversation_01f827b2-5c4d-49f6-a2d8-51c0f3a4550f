/**
 * 导出功能相关API接口
 */

/**
 * 导出API服务类
 */
class ExportAPI {
    constructor(httpClient) {
        this.client = httpClient;
    }

    /**
     * 导出数据
     * @param {Object} params - 导出参数
     * @returns {Promise<Object>} 导出结果
     */
    async exportData(params) {
        try {
            const requestData = {
                data: params.data,
                format: params.format || 'csv', // csv, excel, json, txt
                filename: params.filename || 'export_data',
                options: {
                    includeHeaders: params.includeHeaders !== false,
                    delimiter: params.delimiter || ',',
                    encoding: params.encoding || 'utf-8',
                    compression: params.compression || false
                }
            };

            const response = await this.client.post('/export/data', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        exportId: response.data.exportId,
                        downloadUrl: response.data.downloadUrl,
                        filename: response.data.filename,
                        fileSize: response.data.fileSize,
                        status: response.data.status
                    }
                };
            }

            throw new APIError('数据导出失败', 400);
        } catch (error) {
            console.error('数据导出失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('数据导出失败', 0);
        }
    }

    /**
     * 导出图表
     * @param {Object} params - 导出参数
     * @returns {Promise<Object>} 导出结果
     */
    async exportChart(params) {
        try {
            const requestData = {
                chartType: params.chartType, // line, bar, scatter, etc.
                chartData: params.chartData,
                chartOptions: params.chartOptions,
                format: params.format || 'png', // png, jpg, pdf, svg
                width: params.width || 800,
                height: params.height || 600,
                quality: params.quality || 0.9,
                filename: params.filename || 'chart_export'
            };

            const response = await this.client.post('/export/chart', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        exportId: response.data.exportId,
                        downloadUrl: response.data.downloadUrl,
                        filename: response.data.filename,
                        fileSize: response.data.fileSize,
                        format: response.data.format
                    }
                };
            }

            throw new APIError('图表导出失败', 400);
        } catch (error) {
            console.error('图表导出失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('图表导出失败', 0);
        }
    }

    /**
     * 导出报告
     * @param {Object} params - 导出参数
     * @returns {Promise<Object>} 导出结果
     */
    async exportReport(params) {
        try {
            const requestData = {
                reportType: params.reportType, // analysis, summary, detailed
                data: params.data,
                charts: params.charts || [],
                template: params.template || 'default',
                format: params.format || 'pdf', // pdf, docx, html
                options: {
                    includeCharts: params.includeCharts !== false,
                    includeStatistics: params.includeStatistics !== false,
                    includeRawData: params.includeRawData || false,
                    pageSize: params.pageSize || 'A4',
                    orientation: params.orientation || 'portrait'
                },
                filename: params.filename || 'analysis_report'
            };

            const response = await this.client.post('/export/report', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        exportId: response.data.exportId,
                        downloadUrl: response.data.downloadUrl,
                        filename: response.data.filename,
                        fileSize: response.data.fileSize,
                        pages: response.data.pages
                    }
                };
            }

            throw new APIError('报告导出失败', 400);
        } catch (error) {
            console.error('报告导出失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('报告导出失败', 0);
        }
    }

    /**
     * 查询导出状态
     * @param {string} exportId - 导出ID
     * @returns {Promise<Object>} 导出状态
     */
    async getExportStatus(exportId) {
        try {
            const response = await this.client.get(`/export/status/${exportId}`);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        exportId: response.data.exportId,
                        status: response.data.status, // pending, processing, completed, failed
                        progress: response.data.progress || 0,
                        downloadUrl: response.data.downloadUrl,
                        filename: response.data.filename,
                        fileSize: response.data.fileSize,
                        createdAt: response.data.createdAt,
                        completedAt: response.data.completedAt,
                        error: response.data.error
                    }
                };
            }

            throw new APIError('获取导出状态失败', 400);
        } catch (error) {
            console.error('获取导出状态失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取导出状态失败', 0);
        }
    }

    /**
     * 下载导出文件
     * @param {string} exportId - 导出ID
     * @param {string} filename - 文件名
     * @returns {Promise<void>} 下载结果
     */
    async downloadFile(exportId, filename) {
        try {
            const response = await this.client.get(`/export/download/${exportId}`, {}, {
                responseType: 'blob'
            });

            if (response instanceof Blob) {
                // 创建下载链接
                const url = window.URL.createObjectURL(response);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename || 'download';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                return {
                    success: true,
                    message: '文件下载成功'
                };
            }

            throw new APIError('文件下载失败', 400);
        } catch (error) {
            console.error('文件下载失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('文件下载失败', 0);
        }
    }

    /**
     * 获取导出历史
     * @param {Object} params - 查询参数
     * @returns {Promise<Object>} 导出历史
     */
    async getExportHistory(params = {}) {
        try {
            const queryParams = {
                page: params.page || 1,
                limit: params.limit || 20,
                type: params.type, // data, chart, report
                status: params.status, // pending, processing, completed, failed
                startDate: params.startDate,
                endDate: params.endDate
            };

            const response = await this.client.get('/export/history', queryParams);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        exports: response.data.exports || [],
                        total: response.data.total || 0,
                        page: response.data.page || 1,
                        limit: response.data.limit || 20,
                        totalPages: response.data.totalPages || 0
                    }
                };
            }

            throw new APIError('获取导出历史失败', 400);
        } catch (error) {
            console.error('获取导出历史失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('获取导出历史失败', 0);
        }
    }

    /**
     * 删除导出文件
     * @param {string} exportId - 导出ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteExport(exportId) {
        try {
            const response = await this.client.delete(`/export/${exportId}`);

            if (response.success) {
                return {
                    success: true,
                    message: '导出文件删除成功'
                };
            }

            throw new APIError('删除导出文件失败', 400);
        } catch (error) {
            console.error('删除导出文件失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('删除导出文件失败', 0);
        }
    }

    /**
     * 批量导出
     * @param {Array} exportTasks - 导出任务列表
     * @returns {Promise<Object>} 批量导出结果
     */
    async batchExport(exportTasks) {
        try {
            const requestData = {
                tasks: exportTasks.map(task => ({
                    type: task.type, // data, chart, report
                    params: task.params,
                    filename: task.filename
                })),
                options: {
                    compression: true,
                    archiveFormat: 'zip',
                    archiveName: 'batch_export'
                }
            };

            const response = await this.client.post('/export/batch', requestData);

            if (response.success && response.data) {
                return {
                    success: true,
                    data: {
                        batchId: response.data.batchId,
                        tasks: response.data.tasks,
                        status: response.data.status,
                        downloadUrl: response.data.downloadUrl
                    }
                };
            }

            throw new APIError('批量导出失败', 400);
        } catch (error) {
            console.error('批量导出失败:', error);
            
            if (error instanceof APIError) {
                throw error;
            }
            
            throw new APIError('批量导出失败', 0);
        }
    }

    /**
     * 验证导出参数
     * @param {Object} params - 导出参数
     * @param {string} type - 导出类型
     * @returns {Object} 验证结果
     */
    validateExportParams(params, type) {
        const errors = [];

        if (!params) {
            errors.push('导出参数不能为空');
            return { isValid: false, errors };
        }

        switch (type) {
            case 'data':
                if (!params.data || !Array.isArray(params.data)) {
                    errors.push('数据必须是数组格式');
                }
                if (params.format && !['csv', 'excel', 'json', 'txt'].includes(params.format)) {
                    errors.push('不支持的数据导出格式');
                }
                break;

            case 'chart':
                if (!params.chartType) {
                    errors.push('图表类型不能为空');
                }
                if (!params.chartData) {
                    errors.push('图表数据不能为空');
                }
                if (params.format && !['png', 'jpg', 'pdf', 'svg'].includes(params.format)) {
                    errors.push('不支持的图表导出格式');
                }
                break;

            case 'report':
                if (!params.reportType) {
                    errors.push('报告类型不能为空');
                }
                if (!params.data) {
                    errors.push('报告数据不能为空');
                }
                if (params.format && !['pdf', 'docx', 'html'].includes(params.format)) {
                    errors.push('不支持的报告导出格式');
                }
                break;

            default:
                errors.push('未知的导出类型');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 获取导出进度
     * @param {string} exportId - 导出ID
     * @param {Function} onProgress - 进度回调
     * @param {number} interval - 轮询间隔(毫秒)
     * @returns {Promise<Object>} 最终结果
     */
    async trackExportProgress(exportId, onProgress, interval = 1000) {
        return new Promise((resolve, reject) => {
            const checkProgress = async () => {
                try {
                    const result = await this.getExportStatus(exportId);
                    
                    if (result.success) {
                        const status = result.data.status;
                        const progress = result.data.progress;
                        
                        if (onProgress) {
                            onProgress(progress, status, result.data);
                        }
                        
                        if (status === 'completed') {
                            resolve(result.data);
                        } else if (status === 'failed') {
                            reject(new Error(result.data.error || '导出失败'));
                        } else {
                            setTimeout(checkProgress, interval);
                        }
                    } else {
                        reject(new Error('获取导出状态失败'));
                    }
                } catch (error) {
                    reject(error);
                }
            };
            
            checkProgress();
        });
    }
}

// 创建导出API实例并导出
if (typeof window !== 'undefined' && window.APIService) {
    window.ExportAPI = new ExportAPI(window.APIService.client);
}
