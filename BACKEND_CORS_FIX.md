# 后端CORS配置修复文档

## 问题描述

前端应用在访问后端API时遇到CORS（跨域资源共享）错误：

```
Access to fetch at 'https://cugzcfwwhuiq.sealoshzh.site/v1/auth/register' from origin 'null' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案

### 1. Express.js 后端配置

如果后端使用Express.js，需要配置CORS中间件：

#### 安装CORS中间件
```bash
npm install cors
```

#### 基本配置
```javascript
const express = require('express');
const cors = require('cors');
const app = express();

// 基本CORS配置
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'file://',  // 支持本地文件访问
        'null'      // 支持file://协议
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
    ]
}));

// 处理预检请求
app.options('*', cors());
```

#### 生产环境配置
```javascript
// 生产环境更严格的CORS配置
const corsOptions = {
    origin: function (origin, callback) {
        // 允许的域名列表
        const allowedOrigins = [
            'https://yourdomain.com',
            'https://www.yourdomain.com',
            'http://localhost:3000',
            'file://'  // 本地文件访问
        ];
        
        // 允许没有origin的请求（如移动应用）
        if (!origin) return callback(null, true);
        
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
    ],
    exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
    maxAge: 86400 // 24小时
};

app.use(cors(corsOptions));
```

### 2. 手动CORS配置（不使用中间件）

```javascript
app.use((req, res, next) => {
    // 设置允许的源
    const allowedOrigins = [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'file://',
        'null'
    ];
    
    const origin = req.headers.origin;
    if (allowedOrigins.includes(origin) || !origin) {
        res.setHeader('Access-Control-Allow-Origin', origin || '*');
    }
    
    // 设置允许的方法
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    
    // 设置允许的头部
    res.setHeader('Access-Control-Allow-Headers', 
        'Content-Type, Authorization, X-Requested-With, Accept, Origin');
    
    // 允许携带凭据
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // 处理预检请求
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    next();
});
```

### 3. Nginx 反向代理配置

如果使用Nginx作为反向代理，在nginx.conf中添加：

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location /v1/ {
        # 代理到后端服务
        proxy_pass http://localhost:3001;
        
        # CORS配置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, Accept, Origin' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        
        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, Accept, Origin';
            add_header 'Access-Control-Max-Age' 86400;
            add_header 'Content-Length' 0;
            add_header 'Content-Type' 'text/plain';
            return 204;
        }
        
        # 代理头部设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4. 特殊情况：本地文件访问

由于前端是通过`file://`协议访问的，需要特别处理：

```javascript
// 专门处理file://协议的CORS配置
app.use((req, res, next) => {
    const origin = req.headers.origin;
    
    // 如果是file://协议或者没有origin，允许访问
    if (!origin || origin === 'null' || origin.startsWith('file://')) {
        res.setHeader('Access-Control-Allow-Origin', '*');
    } else {
        // 其他情况按正常CORS处理
        res.setHeader('Access-Control-Allow-Origin', origin);
    }
    
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    next();
});
```

## 验证配置

配置完成后，可以通过以下方式验证：

### 1. 浏览器开发者工具
- 打开Network标签
- 发送请求
- 检查Response Headers中是否包含正确的CORS头部

### 2. curl命令测试
```bash
# 测试预检请求
curl -X OPTIONS \
  -H "Origin: file://" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type, Authorization" \
  https://cugzcfwwhuiq.sealoshzh.site/v1/auth/register

# 测试实际请求
curl -X POST \
  -H "Origin: file://" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456","username":"test"}' \
  https://cugzcfwwhuiq.sealoshzh.site/v1/auth/register
```

## 注意事项

1. **安全性**: 生产环境不要使用`*`作为`Access-Control-Allow-Origin`的值
2. **凭据**: 如果需要发送cookies或认证信息，必须设置`credentials: true`
3. **预检请求**: 复杂请求会先发送OPTIONS预检请求，必须正确处理
4. **缓存**: 可以设置`Access-Control-Max-Age`来缓存预检请求结果

## 推荐配置

对于当前项目，推荐使用以下配置：

```javascript
const corsOptions = {
    origin: function (origin, callback) {
        // 允许file://协议和常见的开发环境
        const allowedOrigins = [
            'http://localhost:3000',
            'http://127.0.0.1:3000',
            'https://yourdomain.com'
        ];
        
        // 允许file://协议访问
        if (!origin || origin === 'null' || origin.startsWith('file://')) {
            return callback(null, true);
        }
        
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        } else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
    ]
};

app.use(cors(corsOptions));
```

这个配置既保证了安全性，又支持本地文件访问。

---

# 完整后端API实现需求文档

## 1. 认证模块 (Authentication)

### 1.1 用户注册接口
```javascript
// POST /v1/auth/register
app.post('/v1/auth/register', async (req, res) => {
    try {
        const { username, email, password } = req.body;

        // 验证输入
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: '请填写完整的注册信息'
                }
            });
        }

        // 检查邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_EMAIL',
                    message: '邮箱格式不正确'
                }
            });
        }

        // 检查密码强度
        if (password.length < 6) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'WEAK_PASSWORD',
                    message: '密码长度至少6位'
                }
            });
        }

        // 检查邮箱是否已存在
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(409).json({
                success: false,
                error: {
                    code: 'EMAIL_EXISTS',
                    message: '该邮箱已被注册，请使用其他邮箱'
                }
            });
        }

        // 创建用户
        const hashedPassword = await bcrypt.hash(password, 10);
        const user = new User({
            username,
            email,
            password: hashedPassword
        });

        await user.save();

        // 生成JWT Token
        const token = jwt.sign(
            { userId: user._id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        res.status(201).json({
            success: true,
            data: {
                token,
                user: {
                    id: user._id,
                    username: user.username,
                    email: user.email
                },
                userId: user._id,
                username: user.username
            }
        });

    } catch (error) {
        console.error('注册失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '服务器内部错误'
            }
        });
    }
});
```

### 1.2 用户登录接口
```javascript
// POST /v1/auth/login
app.post('/v1/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        // 验证输入
        if (!email || !password) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: '请输入邮箱和密码'
                }
            });
        }

        // 查找用户
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: '该邮箱尚未注册，请先注册账号'
                }
            });
        }

        // 验证密码
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'INVALID_CREDENTIALS',
                    message: '邮箱或密码错误，请检查后重试'
                }
            });
        }

        // 生成JWT Token
        const token = jwt.sign(
            { userId: user._id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '7d' }
        );

        res.json({
            success: true,
            data: {
                token,
                user: {
                    id: user._id,
                    username: user.username,
                    email: user.email
                }
            }
        });

    } catch (error) {
        console.error('登录失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '服务器内部错误'
            }
        });
    }
});
```

### 1.3 获取用户信息接口
```javascript
// GET /v1/auth/me
app.get('/v1/auth/me', authenticateToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.userId).select('-password');
        if (!user) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'USER_NOT_FOUND',
                    message: '用户不存在'
                }
            });
        }

        res.json({
            success: true,
            data: {
                user: {
                    id: user._id,
                    username: user.username,
                    email: user.email,
                    createdAt: user.createdAt
                }
            }
        });

    } catch (error) {
        console.error('获取用户信息失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '服务器内部错误'
            }
        });
    }
});
```

## 2. 数据管理模块 (Data Management)

### 2.1 文件上传接口
```javascript
// POST /v1/data/upload
const multer = require('multer');
const upload = multer({
    dest: 'uploads/',
    limits: { fileSize: 50 * 1024 * 1024 } // 50MB
});

app.post('/v1/data/upload', authenticateToken, upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'DATA_001',
                    message: '请选择要上传的文件'
                }
            });
        }

        const { description } = req.body;
        const file = req.file;

        // 验证文件类型
        const allowedTypes = ['.csv', '.xlsx', '.xls', '.json', '.txt'];
        const fileExt = path.extname(file.originalname).toLowerCase();
        if (!allowedTypes.includes(fileExt)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'DATA_001',
                    message: '文件格式不支持'
                }
            });
        }

        // 保存文件记录
        const fileRecord = new DataFile({
            userId: req.user.userId,
            fileName: file.originalname,
            filePath: file.path,
            fileSize: file.size,
            fileType: fileExt,
            description: description || '',
            uploadTime: new Date()
        });

        await fileRecord.save();

        res.json({
            success: true,
            data: {
                fileId: fileRecord._id,
                fileName: fileRecord.fileName,
                fileSize: fileRecord.fileSize,
                uploadTime: fileRecord.uploadTime
            }
        });

    } catch (error) {
        console.error('文件上传失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '文件上传失败'
            }
        });
    }
});
```

### 2.2 数据解析接口
```javascript
// POST /v1/data/parse
app.post('/v1/data/parse', authenticateToken, async (req, res) => {
    try {
        const { fileId, parseOptions = {} } = req.body;

        if (!fileId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: '请提供文件ID'
                }
            });
        }

        // 查找文件记录
        const fileRecord = await DataFile.findById(fileId);
        if (!fileRecord) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'NOT_FOUND',
                    message: '文件不存在'
                }
            });
        }

        // 解析文件数据
        let parsedData = [];
        const filePath = fileRecord.filePath;

        if (fileRecord.fileType === '.csv') {
            parsedData = await parseCSV(filePath, parseOptions);
        } else if (fileRecord.fileType === '.json') {
            parsedData = await parseJSON(filePath);
        }
        // 添加其他文件类型的解析逻辑...

        // 计算统计信息
        const statistics = calculateStatistics(parsedData);

        res.json({
            success: true,
            data: {
                dataId: fileRecord._id,
                parsedData: parsedData.slice(0, 1000), // 只返回前1000个数据点
                metadata: {
                    totalPoints: parsedData.length,
                    sampleRate: parseOptions.sampleRate || 1000,
                    duration: parsedData.length / (parseOptions.sampleRate || 1000),
                    statistics
                }
            }
        });

    } catch (error) {
        console.error('数据解析失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '数据解析失败'
            }
        });
    }
});
```

### 2.3 获取历史数据接口
```javascript
// GET /v1/data/history
app.get('/v1/data/history', authenticateToken, async (req, res) => {
    try {
        const { page = 1, limit = 20, type } = req.query;
        const skip = (page - 1) * limit;

        let query = { userId: req.user.userId };
        if (type) {
            query.type = type;
        }

        const files = await DataFile.find(query)
            .sort({ uploadTime: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await DataFile.countDocuments(query);

        res.json({
            success: true,
            data: {
                items: files.map(file => ({
                    fileId: file._id,
                    fileName: file.fileName,
                    uploadTime: file.uploadTime,
                    dataType: file.fileType,
                    fileSize: file.fileSize,
                    metadata: file.metadata || {}
                })),
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        });

    } catch (error) {
        console.error('获取历史数据失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取历史数据失败'
            }
        });
    }
});
```

## 3. 信号处理模块 (Signal Processing)

### 3.1 FFT分析接口
```javascript
// POST /v1/signal/fft
app.post('/v1/signal/fft', authenticateToken, async (req, res) => {
    try {
        const { data, sampleRate = 1000, windowType = 'hanning' } = req.body;

        if (!data || !Array.isArray(data)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'SIGNAL_001',
                    message: '请提供有效的信号数据'
                }
            });
        }

        // 执行FFT分析
        const fftResult = performFFT(data, sampleRate, windowType);

        res.json({
            success: true,
            data: {
                frequencies: fftResult.frequencies,
                magnitudes: fftResult.magnitudes,
                phases: fftResult.phases,
                powerSpectrum: fftResult.powerSpectrum,
                dominantFrequency: fftResult.dominantFrequency,
                totalPower: fftResult.totalPower
            }
        });

    } catch (error) {
        console.error('FFT分析失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SIGNAL_001',
                message: 'FFT分析失败'
            }
        });
    }
});
```

### 3.2 信号降噪接口
```javascript
// POST /v1/signal/denoise
app.post('/v1/signal/denoise', authenticateToken, async (req, res) => {
    try {
        const { data, method = 'wavelet', threshold = 0.1, waveletType = 'db4' } = req.body;

        if (!data || !Array.isArray(data)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'SIGNAL_001',
                    message: '请提供有效的信号数据'
                }
            });
        }

        // 执行信号降噪
        const denoiseResult = performDenoise(data, method, threshold, waveletType);

        res.json({
            success: true,
            data: {
                denoisedData: denoiseResult.denoisedData,
                noiseData: denoiseResult.noiseData,
                snrImprovement: denoiseResult.snrImprovement,
                denoiseParams: {
                    method,
                    threshold,
                    waveletType
                }
            }
        });

    } catch (error) {
        console.error('信号降噪失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SIGNAL_001',
                message: '信号降噪失败'
            }
        });
    }
});
```

### 3.3 包络分析接口
```javascript
// POST /v1/signal/envelope
app.post('/v1/signal/envelope', authenticateToken, async (req, res) => {
    try {
        const { data, method = 'hilbert', windowSize = 64 } = req.body;

        if (!data || !Array.isArray(data)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'SIGNAL_001',
                    message: '请提供有效的信号数据'
                }
            });
        }

        // 执行包络分析
        const envelopeResult = performEnvelopeAnalysis(data, method, windowSize);

        res.json({
            success: true,
            data: {
                envelopeData: envelopeResult.envelope,
                instantaneousPhase: envelopeResult.phase,
                instantaneousFrequency: envelopeResult.frequency
            }
        });

    } catch (error) {
        console.error('包络分析失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SIGNAL_001',
                message: '包络分析失败'
            }
        });
    }
});
```

### 3.4 信号特征提取接口
```javascript
// POST /v1/signal/features
app.post('/v1/signal/features', authenticateToken, async (req, res) => {
    try {
        const { data, features = ['rms', 'peak', 'crest_factor'], sampleRate = 1000 } = req.body;

        if (!data || !Array.isArray(data)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'SIGNAL_001',
                    message: '请提供有效的信号数据'
                }
            });
        }

        // 提取信号特征
        const extractedFeatures = extractSignalFeatures(data, features, sampleRate);

        res.json({
            success: true,
            data: extractedFeatures
        });

    } catch (error) {
        console.error('特征提取失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SIGNAL_001',
                message: '特征提取失败'
            }
        });
    }
});
```

## 4. 串口通信模块 (Serial Communication)

### 4.1 获取可用串口接口
```javascript
// GET /v1/serial/ports
app.get('/v1/serial/ports', authenticateToken, async (req, res) => {
    try {
        // 获取系统可用串口
        const ports = await getAvailableSerialPorts();

        res.json({
            success: true,
            data: {
                ports: ports.map(port => ({
                    path: port.path,
                    manufacturer: port.manufacturer,
                    serialNumber: port.serialNumber,
                    pnpId: port.pnpId,
                    locationId: port.locationId,
                    vendorId: port.vendorId,
                    productId: port.productId
                }))
            }
        });

    } catch (error) {
        console.error('获取串口列表失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取串口列表失败'
            }
        });
    }
});
```

### 4.2 打开串口连接接口
```javascript
// POST /v1/serial/connect
app.post('/v1/serial/connect', authenticateToken, async (req, res) => {
    try {
        const { port, baudRate = 9600, dataBits = 8, stopBits = 1, parity = 'none' } = req.body;

        if (!port) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: '请指定串口'
                }
            });
        }

        // 打开串口连接
        const connection = await openSerialConnection({
            port,
            baudRate,
            dataBits,
            stopBits,
            parity
        });

        // 创建会话记录
        const session = new SerialSession({
            userId: req.user.userId,
            port,
            baudRate,
            dataBits,
            stopBits,
            parity,
            startTime: new Date(),
            status: 'connected'
        });

        await session.save();

        res.json({
            success: true,
            data: {
                sessionId: session._id,
                port,
                baudRate,
                status: 'connected'
            }
        });

    } catch (error) {
        console.error('串口连接失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '串口连接失败'
            }
        });
    }
});
```

### 4.3 保存串口配置接口
```javascript
// POST /v1/serial/configs
app.post('/v1/serial/configs', authenticateToken, async (req, res) => {
    try {
        const { name, port, baudRate, dataBits, stopBits, parity } = req.body;

        if (!name || !port) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: '请提供配置名称和串口'
                }
            });
        }

        const config = new SerialConfig({
            userId: req.user.userId,
            name,
            port,
            baudRate,
            dataBits,
            stopBits,
            parity,
            createdAt: new Date()
        });

        await config.save();

        res.json({
            success: true,
            data: {
                configId: config._id,
                name: config.name,
                port: config.port,
                baudRate: config.baudRate
            }
        });

    } catch (error) {
        console.error('保存配置失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '保存配置失败'
            }
        });
    }
});
```

### 4.4 获取保存的配置接口
```javascript
// GET /v1/serial/configs
app.get('/v1/serial/configs', authenticateToken, async (req, res) => {
    try {
        const configs = await SerialConfig.find({ userId: req.user.userId })
            .sort({ createdAt: -1 });

        res.json({
            success: true,
            data: {
                configs: configs.map(config => ({
                    id: config._id,
                    name: config.name,
                    port: config.port,
                    baudRate: config.baudRate,
                    dataBits: config.dataBits,
                    stopBits: config.stopBits,
                    parity: config.parity,
                    createdAt: config.createdAt
                }))
            }
        });

    } catch (error) {
        console.error('获取配置列表失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取配置列表失败'
            }
        });
    }
});
```

### 4.5 获取会话历史接口
```javascript
// GET /v1/serial/sessions
app.get('/v1/serial/sessions', authenticateToken, async (req, res) => {
    try {
        const { page = 1, limit = 20, port, startDate, endDate } = req.query;
        const skip = (page - 1) * limit;

        let query = { userId: req.user.userId };
        if (port) query.port = port;
        if (startDate || endDate) {
            query.startTime = {};
            if (startDate) query.startTime.$gte = new Date(startDate);
            if (endDate) query.startTime.$lte = new Date(endDate);
        }

        const sessions = await SerialSession.find(query)
            .sort({ startTime: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await SerialSession.countDocuments(query);

        res.json({
            success: true,
            data: {
                sessions: sessions.map(session => ({
                    id: session._id,
                    port: session.port,
                    baudRate: session.baudRate,
                    startTime: session.startTime,
                    endTime: session.endTime,
                    dataCount: session.dataCount || 0,
                    status: session.status
                })),
                total,
                page: parseInt(page),
                limit: parseInt(limit)
            }
        });

    } catch (error) {
        console.error('获取会话历史失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取会话历史失败'
            }
        });
    }
});
```

## 5. 导出功能模块 (Export)

### 5.1 数据导出接口
```javascript
// POST /v1/export/data
app.post('/v1/export/data', authenticateToken, async (req, res) => {
    try {
        const { data, format = 'csv', filename = 'export_data', options = {} } = req.body;

        if (!data || !Array.isArray(data)) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'EXPORT_001',
                    message: '请提供要导出的数据'
                }
            });
        }

        // 生成导出文件
        const exportResult = await generateExportFile(data, format, filename, options);

        res.json({
            success: true,
            data: {
                exportId: exportResult.exportId,
                downloadUrl: exportResult.downloadUrl,
                filename: exportResult.filename,
                fileSize: exportResult.fileSize,
                status: 'completed'
            }
        });

    } catch (error) {
        console.error('数据导出失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'EXPORT_001',
                message: '数据导出失败'
            }
        });
    }
});
```

### 5.2 图表导出接口
```javascript
// POST /v1/export/chart
app.post('/v1/export/chart', authenticateToken, async (req, res) => {
    try {
        const { chartData, format = 'png', width = 800, height = 600, filename = 'chart' } = req.body;

        if (!chartData) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'EXPORT_001',
                    message: '请提供图表数据'
                }
            });
        }

        // 生成图表文件
        const chartResult = await generateChartFile(chartData, format, width, height, filename);

        res.json({
            success: true,
            data: {
                exportId: chartResult.exportId,
                downloadUrl: chartResult.downloadUrl,
                filename: chartResult.filename,
                fileSize: chartResult.fileSize,
                format: format
            }
        });

    } catch (error) {
        console.error('图表导出失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'EXPORT_001',
                message: '图表导出失败'
            }
        });
    }
});
```

### 5.3 报告导出接口
```javascript
// POST /v1/export/report
app.post('/v1/export/report', authenticateToken, async (req, res) => {
    try {
        const { reportType, data, charts = [], template = 'default', format = 'pdf', options = {}, filename = 'analysis_report' } = req.body;

        if (!reportType || !data) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'EXPORT_001',
                    message: '请提供报告类型和数据'
                }
            });
        }

        // 生成报告文件
        const reportResult = await generateReportFile(reportType, data, charts, template, format, options, filename);

        res.json({
            success: true,
            data: {
                exportId: reportResult.exportId,
                downloadUrl: reportResult.downloadUrl,
                filename: reportResult.filename,
                fileSize: reportResult.fileSize,
                format: format,
                reportType: reportType
            }
        });

    } catch (error) {
        console.error('报告导出失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'EXPORT_001',
                message: '报告导出失败'
            }
        });
    }
});
```

### 5.4 批量导出接口
```javascript
// POST /v1/export/batch
app.post('/v1/export/batch', authenticateToken, async (req, res) => {
    try {
        const { tasks, options = {} } = req.body;

        if (!tasks || !Array.isArray(tasks) || tasks.length === 0) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'EXPORT_001',
                    message: '请提供导出任务列表'
                }
            });
        }

        // 执行批量导出
        const batchResult = await performBatchExport(tasks, options);

        res.json({
            success: true,
            data: {
                batchId: batchResult.batchId,
                tasks: batchResult.tasks,
                status: batchResult.status,
                downloadUrl: batchResult.downloadUrl
            }
        });

    } catch (error) {
        console.error('批量导出失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'EXPORT_001',
                message: '批量导出失败'
            }
        });
    }
});
```

## 6. 反馈系统模块 (Feedback)

### 6.1 提交反馈接口
```javascript
// POST /v1/feedback
app.post('/v1/feedback', async (req, res) => {
    try {
        const { title, content, category, priority = 'medium', contactInfo } = req.body;

        if (!title || !content || !category) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: '请填写完整的反馈信息'
                }
            });
        }

        // 生成跟踪号
        const trackingNumber = generateTrackingNumber();

        const feedback = new Feedback({
            title,
            content,
            category,
            priority,
            contactInfo,
            trackingNumber,
            status: 'open',
            createdAt: new Date()
        });

        await feedback.save();

        res.json({
            success: true,
            data: {
                id: feedback._id,
                trackingNumber: feedback.trackingNumber,
                title: feedback.title,
                category: feedback.category,
                priority: feedback.priority,
                status: feedback.status,
                createdAt: feedback.createdAt
            }
        });

    } catch (error) {
        console.error('提交反馈失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '提交反馈失败'
            }
        });
    }
});
```

### 6.2 获取反馈列表接口
```javascript
// GET /v1/feedback
app.get('/v1/feedback', async (req, res) => {
    try {
        const { page = 1, limit = 20, type, status, priority, category, search } = req.query;
        const skip = (page - 1) * limit;

        let query = {};
        if (type) query.type = type;
        if (status) query.status = status;
        if (priority) query.priority = priority;
        if (category) query.category = category;
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { content: { $regex: search, $options: 'i' } }
            ];
        }

        const feedbacks = await Feedback.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await Feedback.countDocuments(query);

        res.json({
            success: true,
            data: {
                feedbacks: feedbacks.map(feedback => ({
                    id: feedback._id,
                    title: feedback.title,
                    category: feedback.category,
                    priority: feedback.priority,
                    status: feedback.status,
                    trackingNumber: feedback.trackingNumber,
                    createdAt: feedback.createdAt,
                    updatedAt: feedback.updatedAt
                })),
                total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('获取反馈列表失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取反馈列表失败'
            }
        });
    }
});
```

### 6.3 根据跟踪号查询反馈接口
```javascript
// GET /v1/feedback/tracking/:trackingNumber
app.get('/v1/feedback/tracking/:trackingNumber', async (req, res) => {
    try {
        const { trackingNumber } = req.params;

        const feedback = await Feedback.findOne({ trackingNumber });
        if (!feedback) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'NOT_FOUND',
                    message: '未找到该跟踪号对应的反馈'
                }
            });
        }

        res.json({
            success: true,
            data: {
                id: feedback._id,
                title: feedback.title,
                category: feedback.category,
                priority: feedback.priority,
                status: feedback.status,
                trackingNumber: feedback.trackingNumber,
                createdAt: feedback.createdAt,
                updatedAt: feedback.updatedAt,
                commentsCount: feedback.comments ? feedback.comments.length : 0,
                latestUpdate: feedback.updatedAt
            }
        });

    } catch (error) {
        console.error('查询反馈失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '查询反馈失败'
            }
        });
    }
});
```

## 7. 统计模块 (Statistics)

### 7.1 系统统计接口
```javascript
// GET /v1/stats/system
app.get('/v1/stats/system', authenticateToken, async (req, res) => {
    try {
        // 获取系统统计数据
        const totalUsers = await User.countDocuments();
        const totalFiles = await DataFile.countDocuments();
        const totalFeedbacks = await Feedback.countDocuments();
        const totalSessions = await SerialSession.countDocuments();

        // 获取今日统计
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const todayUploads = await DataFile.countDocuments({ uploadTime: { $gte: today } });
        const todayFeedbacks = await Feedback.countDocuments({ createdAt: { $gte: today } });

        res.json({
            success: true,
            data: {
                overview: {
                    totalUsers,
                    totalFiles,
                    totalFeedbacks,
                    totalSessions
                },
                today: {
                    uploads: todayUploads,
                    feedbacks: todayFeedbacks
                },
                serverStatus: {
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    timestamp: new Date().toISOString()
                }
            }
        });

    } catch (error) {
        console.error('获取系统统计失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取系统统计失败'
            }
        });
    }
});
```

### 7.2 用户活动统计接口
```javascript
// GET /v1/stats/user-activity
app.get('/v1/stats/user-activity', authenticateToken, async (req, res) => {
    try {
        const { period = 'week', startDate, endDate } = req.query;

        let start, end;
        if (startDate && endDate) {
            start = new Date(startDate);
            end = new Date(endDate);
        } else {
            end = new Date();
            switch (period) {
                case 'day':
                    start = new Date(end.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case 'week':
                    start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
            }
        }

        // 获取时间线数据
        const timeline = await generateActivityTimeline(req.user.userId, start, end);

        // 获取汇总数据
        const summary = await getUserActivitySummary(req.user.userId, start, end);

        res.json({
            success: true,
            data: {
                timeline,
                summary,
                period: {
                    start: start.toISOString(),
                    end: end.toISOString(),
                    type: period
                }
            }
        });

    } catch (error) {
        console.error('获取用户活动统计失败:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: '获取用户活动统计失败'
            }
        });
    }
});
```

## 8. WebSocket实时通信模块

### 8.1 WebSocket服务器设置
```javascript
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

// 创建WebSocket服务器
const wss = new WebSocket.Server({
    port: 8080,
    verifyClient: (info) => {
        // 验证WebSocket连接
        const token = new URL(info.req.url, 'http://localhost').searchParams.get('token');
        if (!token) return false;

        try {
            jwt.verify(token, process.env.JWT_SECRET);
            return true;
        } catch (error) {
            return false;
        }
    }
});

wss.on('connection', (ws, req) => {
    const token = new URL(req.url, 'http://localhost').searchParams.get('token');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    ws.userId = decoded.userId;
    ws.isAlive = true;

    console.log(`用户 ${ws.userId} 已连接WebSocket`);

    // 发送连接成功消息
    ws.send(JSON.stringify({
        type: 'connection',
        data: { status: 'connected', userId: ws.userId }
    }));

    // 处理消息
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            handleWebSocketMessage(ws, data);
        } catch (error) {
            console.error('WebSocket消息解析失败:', error);
        }
    });

    // 处理连接关闭
    ws.on('close', () => {
        console.log(`用户 ${ws.userId} 断开WebSocket连接`);
    });

    // 心跳检测
    ws.on('pong', () => {
        ws.isAlive = true;
    });
});

// 心跳检测
setInterval(() => {
    wss.clients.forEach((ws) => {
        if (!ws.isAlive) {
            ws.terminate();
            return;
        }
        ws.isAlive = false;
        ws.ping();
    });
}, 30000);
```

### 8.2 WebSocket消息处理
```javascript
function handleWebSocketMessage(ws, data) {
    switch (data.type) {
        case 'serial_data':
            handleSerialData(ws, data);
            break;
        case 'analysis_progress':
            handleAnalysisProgress(ws, data);
            break;
        case 'system_notification':
            handleSystemNotification(ws, data);
            break;
        default:
            console.log('未知的WebSocket消息类型:', data.type);
    }
}

function handleSerialData(ws, data) {
    // 处理串口数据
    console.log('收到串口数据:', data);

    // 广播给其他连接的客户端
    wss.clients.forEach((client) => {
        if (client !== ws && client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'serial_data',
                data: {
                    timestamp: new Date().toISOString(),
                    content: data.content,
                    direction: 'received'
                }
            }));
        }
    });
}
```

## 9. 中间件和工具函数

### 9.1 JWT认证中间件
```javascript
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({
            success: false,
            error: {
                code: 'AUTH_002',
                message: '未提供认证令牌'
            }
        });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'AUTH_002',
                    message: '认证令牌无效或已过期'
                }
            });
        }

        req.user = user;
        next();
    });
}
```

### 9.2 错误处理中间件
```javascript
function errorHandler(err, req, res, next) {
    console.error('服务器错误:', err);

    // 处理不同类型的错误
    if (err.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: '输入数据验证失败',
                details: err.message
            }
        });
    }

    if (err.name === 'CastError') {
        return res.status(400).json({
            success: false,
            error: {
                code: 'INVALID_ID',
                message: '无效的ID格式'
            }
        });
    }

    // 默认错误响应
    res.status(500).json({
        success: false,
        error: {
            code: 'INTERNAL_ERROR',
            message: '服务器内部错误'
        }
    });
}
```

### 9.3 数据库模型定义
```javascript
// 用户模型
const userSchema = new mongoose.Schema({
    username: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    createdAt: { type: Date, default: Date.now }
});

// 数据文件模型
const dataFileSchema = new mongoose.Schema({
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    fileName: { type: String, required: true },
    filePath: { type: String, required: true },
    fileSize: { type: Number, required: true },
    fileType: { type: String, required: true },
    description: { type: String },
    uploadTime: { type: Date, default: Date.now },
    metadata: { type: Object }
});

// 串口会话模型
const serialSessionSchema = new mongoose.Schema({
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    port: { type: String, required: true },
    baudRate: { type: Number, required: true },
    dataBits: { type: Number, default: 8 },
    stopBits: { type: Number, default: 1 },
    parity: { type: String, default: 'none' },
    startTime: { type: Date, default: Date.now },
    endTime: { type: Date },
    status: { type: String, enum: ['connected', 'disconnected', 'error'], default: 'connected' },
    dataCount: { type: Number, default: 0 }
});

// 反馈模型
const feedbackSchema = new mongoose.Schema({
    title: { type: String, required: true },
    content: { type: String, required: true },
    category: { type: String, required: true },
    priority: { type: String, enum: ['low', 'medium', 'high', 'urgent'], default: 'medium' },
    status: { type: String, enum: ['open', 'in_progress', 'resolved', 'closed'], default: 'open' },
    trackingNumber: { type: String, unique: true, required: true },
    contactInfo: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    comments: [{ type: Object }]
});
```

## 10. 部署和配置说明

### 10.1 环境变量配置
```bash
# .env 文件
NODE_ENV=production
PORT=3001
JWT_SECRET=your_jwt_secret_key_here
MONGODB_URI=mongodb://localhost:27017/dataplatform
CORS_ORIGIN=https://yourdomain.com,http://localhost:3000,file://,null
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=52428800
```

### 10.2 启动脚本
```javascript
// server.js
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
require('dotenv').config();

const app = express();

// CORS配置
const corsOptions = {
    origin: function (origin, callback) {
        const allowedOrigins = process.env.CORS_ORIGIN.split(',');
        if (!origin || origin === 'null' || allowedOrigins.includes(origin)) {
            return callback(null, true);
        }
        callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 连接数据库
mongoose.connect(process.env.MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// 路由
app.use('/v1/auth', authRoutes);
app.use('/v1/data', dataRoutes);
app.use('/v1/signal', signalRoutes);
app.use('/v1/serial', serialRoutes);
app.use('/v1/export', exportRoutes);
app.use('/v1/feedback', feedbackRoutes);
app.use('/v1/stats', statsRoutes);

// 错误处理
app.use(errorHandler);

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
});
```

这个完整的后端实现文档涵盖了前端系统所需的所有API接口和功能模块。
